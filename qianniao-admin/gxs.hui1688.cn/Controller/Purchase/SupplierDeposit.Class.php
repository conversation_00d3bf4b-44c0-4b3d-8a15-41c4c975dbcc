<?php
/**
 * 供应商保证金账户控制器
 *
 * @copyright   Copyright (c) https://www.qianniaovip.com All rights reserved
 */

namespace JinDouYun\Controller\Purchase;

use JinDou<PERSON>un\Common\SupplierDepositType;
use <PERSON><PERSON><PERSON><PERSON>un\Controller\BaseController;
use <PERSON><PERSON><PERSON><PERSON><PERSON>\Controller\Common\Logger;
use <PERSON><PERSON><PERSON>Y<PERSON>\Model\Purchase\MSupplierDeposit;
use Mall\Framework\Core\ErrorCode;

class SupplierDeposit extends BaseController
{
    /**
     * 供应商保证金账户模型
     * @var MSupplierDeposit
     */
    private $objMSupplierDeposit;

    /**
     * 构造函数
     *
     * @param bool $isCheckAcl 是否检查权限
     * @param bool $isMustLogin 是否必须登录
     */
    public function __construct($isCheckAcl = true, $isMustLogin = true)
    {
        parent::__construct($isCheckAcl, $isMustLogin);
        $this->objMSupplierDeposit = new MSupplierDeposit($this->onlineEnterpriseId, $this->onlineUserId);
    }

    /**
     * 获取当前登录供应商ID（供应商角色端专用）
     *
     * @return int 供应商ID
     * @throws \Exception 当供应商未登录或无权限时抛出异常
     */
    protected function getCurrentSupplierId()
    {
        if (empty($this->supplierId)) {
            $this->sendOutput('供应商未登录或无权限', ErrorCode::$notAllowAccess);
        }
        return $this->supplierId;
    }

    /**
     * 验证供应商权限（供应商角色端专用）
     * 确保当前登录的供应商只能访问自己的数据
     *
     * @param int $targetSupplierId 目标供应商ID
     * @return bool 验证通过返回true
     * @throws \Exception 当权限验证失败时抛出异常
     */
    protected function validateSupplierPermission($targetSupplierId)
    {
        $currentSupplierId = $this->getCurrentSupplierId();

        if ($currentSupplierId != $targetSupplierId) {
            $this->sendOutput('无权限访问其他供应商数据', ErrorCode::$notAllowAccess);
        }

        return true;
    }

    /**
     * 获取供应商保证金账户信息
     */
    public function getDepositAccount()
    {
        $params = $this->request->getRawJson();

        if (empty($params['supplierId'])) {
            $this->sendOutput('供应商ID不能为空', ErrorCode::$paramError);
        }

        $supplierId = intval($params['supplierId']);

        $result = $this->objMSupplierDeposit->getDepositAccount($supplierId);

        if ($result->isSuccess()) {
            $this->sendOutput($result->getData());
        } else {
            $this->sendOutput($result->getData(), $result->getErrorCode());
        }
    }

    /**
     * 保证金充值
     */
    public function deposit()
    {
        $params = $this->request->getRawJson();

        if (empty($params['supplierId'])) {
            $this->sendOutput('供应商ID不能为空', ErrorCode::$paramError);
        }

        if (empty($params['amount']) || !is_numeric($params['amount']) || floatval($params['amount']) <= 0) {
            $this->sendOutput('充值金额必须大于0', ErrorCode::$paramError);
        }

        $supplierId = intval($params['supplierId']);
        $amount = floatval($params['amount']);
        $remark = isset($params['remark']) ? $params['remark'] : '';

        $result = $this->objMSupplierDeposit->deposit($supplierId, $amount, $remark);

        if ($result->isSuccess()) {
            $this->sendOutput($result->getData());
        } else {
            $this->sendOutput($result->getData(), $result->getErrorCode());
        }
    }

    /**
     * 保证金扣除
     */
    public function deduct()
    {
        $params = $this->request->getRawJson();

        if (empty($params['supplierId'])) {
            $this->sendOutput('供应商ID不能为空', ErrorCode::$paramError);
        }

        if (empty($params['amount']) || !is_numeric($params['amount']) || floatval($params['amount']) <= 0) {
            $this->sendOutput('扣除金额必须大于0', ErrorCode::$paramError);
        }

        $supplierId = intval($params['supplierId']);
        $amount = floatval($params['amount']);
        $remark = isset($params['remark']) ? $params['remark'] : '';

        $result = $this->objMSupplierDeposit->deduct($supplierId, $amount, $remark);

        if ($result->isSuccess()) {
            $this->sendOutput($result->getData());
        } else {
            $this->sendOutput($result->getData(), $result->getErrorCode());
        }
    }

    /**
     * 保证金冻结
     */
    public function freeze()
    {
        $params = $this->request->getRawJson();

        if (empty($params['supplierId'])) {
            $this->sendOutput('供应商ID不能为空', ErrorCode::$paramError);
        }

        if (empty($params['amount']) || !is_numeric($params['amount']) || floatval($params['amount']) <= 0) {
            $this->sendOutput('冻结金额必须大于0', ErrorCode::$paramError);
        }

        $supplierId = intval($params['supplierId']);
        $amount = floatval($params['amount']);
        $remark = isset($params['remark']) ? $params['remark'] : '';

        $result = $this->objMSupplierDeposit->freeze($supplierId, $amount, $remark);

        if ($result->isSuccess()) {
            $this->sendOutput($result->getData());
        } else {
            $this->sendOutput($result->getData(), $result->getErrorCode());
        }
    }

    /**
     * 保证金解冻
     */
    public function unfreeze()
    {
        $params = $this->request->getRawJson();

        if (empty($params['supplierId'])) {
            $this->sendOutput('供应商ID不能为空', ErrorCode::$paramError);
        }

        if (empty($params['amount']) || !is_numeric($params['amount']) || floatval($params['amount']) <= 0) {
            $this->sendOutput('解冻金额必须大于0', ErrorCode::$paramError);
        }

        $supplierId = intval($params['supplierId']);
        $amount = floatval($params['amount']);
        $remark = isset($params['remark']) ? $params['remark'] : '';

        $result = $this->objMSupplierDeposit->unfreeze($supplierId, $amount, $remark);

        if ($result->isSuccess()) {
            $this->sendOutput($result->getData());
        } else {
            $this->sendOutput($result->getData(), $result->getErrorCode());
        }
    }

    /**
     * 获取保证金流水记录
     */
    public function getDepositDetails()
    {
        $params = $this->request->getRawJson();

        if (empty($params['supplierId'])) {
            $this->sendOutput('供应商ID不能为空', ErrorCode::$paramError);
        }

        $result = $this->objMSupplierDeposit->getDepositDetails($params);

        if ($result->isSuccess()) {
            $data = $result->getData();
            $pageData = [
                'pageIndex' => isset($params['page']) ? intval($params['page']) : 1,
                'pageSize' => isset($params['pageSize']) ? intval($params['pageSize']) : 10,
                'pageTotal' => $data['total'],
            ];
            $this->sendOutput($data, 0, $pageData);
        } else {
            $this->sendOutput($result->getData(), $result->getErrorCode());
        }
    }

    /**
     * 获取保证金历史记录
     * 实现任务10.1.4 getDepositHistory
     */
    public function getDepositHistory()
    {
        $params = $this->request->getRawJson();

        if (empty($params['supplierId'])) {
            $this->sendOutput('供应商ID不能为空', ErrorCode::$paramError);
        }

        $result = $this->objMSupplierDeposit->getDepositDetails($params);

        if ($result->isSuccess()) {
            $data = $result->getData();
            $pageData = [
                'pageIndex' => isset($params['page']) ? intval($params['page']) : 1,
                'pageSize' => isset($params['pageSize']) ? intval($params['pageSize']) : 10,
                'pageTotal' => $data['total'],
            ];
            $this->sendOutput($data, 0, $pageData);
        } else {
            $this->sendOutput($result->getData(), $result->getErrorCode());
        }
    }

    /**
     * 导出保证金历史记录
     * 实现任务10.1.5 exportDepositHistory
     */
    public function exportDepositHistory()
    {
        $params = $this->request->getRawJson();

        if (empty($params['supplierId'])) {
            $this->sendOutput('供应商ID不能为空', ErrorCode::$paramError);
        }

        // 不分页，获取所有符合条件的记录
        $params['pageSize'] = 10000; // 设置一个较大的值
        $params['page'] = 1;

        $result = $this->objMSupplierDeposit->getDepositDetails($params);

        if (!$result->isSuccess()) {
            $this->sendOutput($result->getData(), $result->getErrorCode());
            return;
        }

        $data = $result->getData();
        $supplier = $data['supplier'];
        $list = $data['list'];

        // 准备Excel导出数据
        $exportData = [
            'fileName' => '供应商保证金历史记录_' . $supplier['title'] . '_' . date('YmdHis') . '.xlsx',
            'sheets' => [
                [
                    'sheetName' => '保证金历史记录',
                    'headers' => [
                        '流水号', '操作类型', '金额(元)', '操作前余额(元)', '操作后余额(元)',
                        '操作人', '操作时间', '备注'
                    ],
                    'data' => []
                ]
            ]
        ];

        // 填充数据
        foreach ($list as $item) {
            $exportData['sheets'][0]['data'][] = [
                $item['no'],
                $item['operationTypeName'],
                $item['amount'],
                $item['beforeAmount'],
                $item['afterAmount'],
                $item['operatorName'],
                $item['receiptTimeFormat'],
                $item['remark']
            ];
        }

        // 添加汇总信息
        $exportData['summary'] = [
            '供应商名称' => $supplier['title'],
            '当前保证金余额' => $supplier['depositAccount'] . '元',
            '冻结金额' => $supplier['frozenDeposit'] . '元',
            '可用余额' => $supplier['availableDeposit'] . '元',
            '导出时间' => date('Y-m-d H:i:s'),
            '导出人' => '系统管理员'
        ];

        $this->sendOutput($exportData);
    }

    /**
     * 获取保证金操作历史记录
     * 实现任务10.1.6 getDepositOperationHistory
     */
    public function getDepositOperationHistory()
    {
        $params = $this->request->getRawJson();

        if (empty($params['supplierId'])) {
            $this->sendOutput('供应商ID不能为空', ErrorCode::$paramError);
        }

        $result = $this->objMSupplierDeposit->getDepositOperationHistory($params);

        if ($result->isSuccess()) {
            $data = $result->getData();
            $pageData = [
                'pageIndex' => isset($params['page']) ? intval($params['page']) : 1,
                'pageSize' => isset($params['pageSize']) ? intval($params['pageSize']) : 10,
                'pageTotal' => $data['total'],
            ];
            $this->sendOutput($data, 0, $pageData);
        } else {
            $this->sendOutput($result->getData(), $result->getErrorCode());
        }
    }

    /**
     * 获取保证金操作类型列表
     */
    public function getOperationTypes()
    {
        $types = SupplierDepositType::getAllTypes();
        $this->sendOutput($types);
    }

    /**
     * 获取当前登录供应商的保证金账户余额
     * 供应商角色端使用
     */
    public function getSupplierOwnDepositAccount()
    {
        // 获取当前登录供应商ID
        $supplierId = $this->getSupplierId();
        if (empty($supplierId)) {
            $this->sendOutput('未找到供应商信息', ErrorCode::$paramError);
        }

        $result = $this->objMSupplierDeposit->getDepositAccount($supplierId);

        if ($result->isSuccess()) {
            $this->sendOutput($result->getData());
        } else {
            $this->sendOutput($result->getData(), $result->getErrorCode());
        }
    }

    /**
     * 获取当前登录供应商的保证金流水记录
     * 供应商角色端使用
     */
    public function getSupplierOwnDepositHistory()
    {
        // 获取当前登录供应商ID
        $supplierId = $this->getSupplierId();
        if (empty($supplierId)) {
            $this->sendOutput('未找到供应商信息', ErrorCode::$paramError);
        }

        $params = $this->request->getRawJson();
        $params['supplierId'] = $supplierId;

        $result = $this->objMSupplierDeposit->getDepositDetails($params);

        if ($result->isSuccess()) {
            $data = $result->getData();
            $pageData = [
                'pageIndex' => isset($params['page']) ? intval($params['page']) : 1,
                'pageSize' => isset($params['pageSize']) ? intval($params['pageSize']) : 10,
                'pageTotal' => $data['total'],
            ];
            $this->sendOutput($data, 0, $pageData);
        } else {
            $this->sendOutput($result->getData(), $result->getErrorCode());
        }
    }
}

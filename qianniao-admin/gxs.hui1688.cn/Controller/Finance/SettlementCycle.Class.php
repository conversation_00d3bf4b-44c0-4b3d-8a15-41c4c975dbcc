<?php
/**
 * 结算周期控制器类
 * @package Jin<PERSON><PERSON><PERSON><PERSON>\Controller\Finance
 * <AUTHOR>
 * @date 2023/08/16
 */

namespace Jin<PERSON><PERSON><PERSON><PERSON>\Controller\Finance;

use <PERSON><PERSON><PERSON><PERSON><PERSON>\Controller\BaseController;
use <PERSON><PERSON><PERSON><PERSON><PERSON>\Model\Department\MStaff;
use <PERSON><PERSON><PERSON><PERSON><PERSON>\Model\Finance\MSettlementCycle;
use <PERSON><PERSON><PERSON>Y<PERSON>\Model\Manage\MUser;
use Mall\Framework\Core\ErrorCode;

/**
 * 结算周期控制器类
 * @package JinDouYun\Controller\Finance
 */
class SettlementCycle extends BaseController
{
    /**
     * 结算周期模型
     * @var MSettlementCycle
     */
    protected $objMSettlementCycle;

    /**
     * 构造函数
     * @param bool $isCheckAcl 是否检查权限
     * @param bool $isMustLogin 是否必须登录
     */
    public function __construct($isCheckAcl = true, $isMustLogin = true)
    {
        parent::__construct($isCheckAcl, $isMustLogin);
        $this->objMSettlementCycle = new MSettlementCycle($this->onlineEnterpriseId, $this->onlineUserId);
    }

    /**
     * 公共字段过滤方法
     * @return array 过滤后的参数
     */
    public function commonFieldFilter()
    {
        $params = $this->request->getRawJson();
        if (empty($params)) {
            $this->sendOutput('参数为空', ErrorCode::$paramError);
        }

        $returnData = [];

        // 周期类型
        if (isset($params['cycleType']) && !empty($params['cycleType'])) {
            $returnData['cycleType'] = intval($params['cycleType']);
        }

        // 周期天数（T+N模式）
        if (isset($params['cycleDays'])) {
            $returnData['cycleDays'] = intval($params['cycleDays']);
        }

        // 周期日期（月结等模式）
        if (isset($params['cycleDay'])) {
            $returnData['cycleDay'] = intval($params['cycleDay']);
        }

        // 供应商ID
        if (isset($params['supplierId'])) {
            $returnData['supplierId'] = intval($params['supplierId']);
        }

        // 状态
        if (isset($params['status'])) {
            $returnData['status'] = intval($params['status']);
        }

        return $returnData;
    }

    /**
     * 添加结算周期配置
     */
    public function addCycle()
    {
        $data = $this->commonFieldFilter();

        $result = $this->objMSettlementCycle->addCycle($data);
        if ($result->isSuccess()) {
            $this->sendOutput($result->getData());
        } else {
            $this->sendOutput($result->getData(), $result->getErrorCode());
        }
    }

    /**
     * 更新结算周期配置
     */
    public function updateCycle()
    {
        $id = $this->request->param('request_id');
        if (empty($id)) {
            $this->sendOutput('配置ID不能为空', ErrorCode::$paramError);
        }

        $data = $this->commonFieldFilter();

        $result = $this->objMSettlementCycle->updateCycle($id, $data);
        if ($result->isSuccess()) {
            $this->sendOutput($result->getData());
        } else {
            $this->sendOutput($result->getData(), $result->getErrorCode());
        }
    }

    /**
     * 获取结算周期配置详情
     */
    public function getCycleDetail()
    {
        $id = $this->request->param('request_id');
        if (empty($id)) {
            $this->sendOutput('配置ID不能为空', ErrorCode::$paramError);
        }

        $result = $this->objMSettlementCycle->getCycleDetail($id);
        if ($result->isSuccess()) {
            $this->sendOutput($result->getData());
        } else {
            $this->sendOutput($result->getData(), $result->getErrorCode());
        }
    }

    /**
     * 获取结算周期配置列表
     */
    public function getCycleList()
    {
        $params = $this->request->getRawJson();
        if (empty($params)) {
            $params = [];
        }

        $result = $this->objMSettlementCycle->getCycleList($params);
        if ($result->isSuccess()) {
            $data = $result->getData();
            $pageData = [
                'pageIndex' => isset($params['page']) ? intval($params['page']) : 1,
                'pageSize' => isset($params['pageSize']) ? intval($params['pageSize']) : 20,
                'pageTotal' => $data['total']
            ];
            $this->sendOutput($data['list'], 0, $pageData);
        } else {
            $this->sendOutput($result->getData(), $result->getErrorCode());
        }
    }

    /**
     * 删除结算周期配置
     */
    public function deleteCycle()
    {
        $id = $this->request->param('request_id');
        if (empty($id)) {
            $this->sendOutput('配置ID不能为空', ErrorCode::$paramError);
        }

        $result = $this->objMSettlementCycle->deleteCycle($id);
        if ($result->isSuccess()) {
            $this->sendOutput($result->getData());
        } else {
            $this->sendOutput($result->getData(), $result->getErrorCode());
        }
    }

    /**
     * 根据供应商ID获取结算周期配置
     */
    public function getCycleBySupplier()
    {
        $supplierId = $this->request->param('supplierId');
        if (empty($supplierId)) {
            $this->sendOutput('供应商ID不能为空', ErrorCode::$paramError);
        }

        $result = $this->objMSettlementCycle->getCycleBySupplier($supplierId);
        if ($result->isSuccess()) {
            $data = $result->getData();
            // 确保返回useDefaultConfig字段，告诉前端是否是默认配置
            if (!isset($data['useDefaultConfig'])) {
                $data['useDefaultConfig'] = empty($data['supplierId']) || $data['supplierId'] == 0;
            }
            $this->sendOutput($data);
        } else {
            $this->sendOutput($result->getData(), $result->getErrorCode());
        }
    }

    /**
     * 获取结算周期配置
     * 如果不传供应商ID，则获取默认配置
     * 如果传入供应商ID，则获取该供应商的专属配置
     */
    public function getSettlementCycle()
    {
        $params = $this->request->getRawJson();
        $supplierId = isset($params['supplierId']) ? intval($params['supplierId']) : 0;

        // 调用结算周期模型获取配置
        $result = $this->objMSettlementCycle->getCycleBySupplier($supplierId);

        if ($result->isSuccess()) {
            $this->sendOutput($result->getData());
        } else {
            $this->sendOutput($result->getData(), $result->getErrorCode());
        }
    }

    /**
     * 更新结算周期配置
     * 如果设置useDefaultConfig为true，则使用默认配置
     * 否则使用自定义配置
     */
    public function updateSettlementCycle()
    {
        $params = $this->request->getRawJson();

        if (empty($params)) {
            $this->sendOutput('参数不能为空', ErrorCode::$paramError);
        }

        // 检查是否是默认配置
        $useDefaultConfig = isset($params['useDefaultConfig']) && $params['useDefaultConfig'];
        $supplierId = isset($params['supplierId']) ? intval($params['supplierId']) : 0;

        // 处理默认配置的情况
        if ($useDefaultConfig) {
            // 如果是更新默认配置本身（supplierId=0）
            if ($supplierId === 0) {
                // 检查必要字段
                if (!isset($params['cycleType'])) {
                    $this->sendOutput('周期类型不能为空', ErrorCode::$paramError);
                }

                // 构建默认配置数据
                $configData = [
                    'supplierId' => 0, // 默认配置的supplierId为0
                    'cycleType' => intval($params['cycleType']),
                    'cycleDay' => isset($params['cycleDay']) ? intval($params['cycleDay']) : 1,
                    'cycleMonth' => isset($params['cycleMonth']) ? intval($params['cycleMonth']) : 1,
                    'cycleDays' => isset($params['cycleDays']) ? intval($params['cycleDays']) : 7,
                    'autoGenerate' => isset($params['autoGenerate']) ? ($params['autoGenerate'] ? true : false) : true,
                    'autoApprove' => isset($params['autoApprove']) ? ($params['autoApprove'] ? true : false) : false,
                    'minSettlementAmount' => isset($params['minSettlementAmount']) ? floatval($params['minSettlementAmount']) : 0,
                    'notificationEnabled' => isset($params['notificationEnabled']) ? ($params['notificationEnabled'] ? true : false) : true,
                    'notificationMethods' => isset($params['notificationMethods']) ? $params['notificationMethods'] : ['email', 'system'],
                    'status' => isset($params['status']) ? intval($params['status']) : 5, // 默认启用
                    'useDefaultConfig' => $useDefaultConfig,
                ];

                // 查询是否已有默认配置
                $defaultCycleResult = $this->objMSettlementCycle->getDefaultCycle();

                if ($defaultCycleResult->isSuccess()) {
                    $defaultCycleData = $defaultCycleResult->getData();

                    if (!empty($defaultCycleData) && isset($defaultCycleData['id'])) {
                        // 已有默认配置，更新
                        $result = $this->objMSettlementCycle->updateCycle($defaultCycleData['id'], $configData);
                    } else {
                        // 没有默认配置，新增
                        $result = $this->objMSettlementCycle->addCycle($configData);
                    }

                    if ($result->isSuccess()) {
                        $this->sendOutput(['message' => '默认配置保存成功']);
                    } else {
                        $this->sendOutput($result->getData(), $result->getErrorCode());
                    }
                } else {
                    $this->sendOutput($defaultCycleResult->getData(), $defaultCycleResult->getErrorCode());
                }

                return;
            }

            // 如果是将供应商配置切换为使用默认配置
            if ($supplierId > 0) {
                // 查询该供应商是否有专属配置
                $cycleResult = $this->objMSettlementCycle->getCycleBySupplier($supplierId);

                if ($cycleResult->isSuccess()) {
                    $cycleData = $cycleResult->getData();

                    // 如果有专属配置且不是使用默认配置，则删除
                    if (!empty($cycleData) && isset($cycleData['id']) && !$cycleData['useDefaultConfig']) {
                        // 设置为删除状态
                        $updateData = [
                            'deleteStatus' => 4, // 删除状态
                            'updateUserId' => $this->onlineUserId,
                            'updateUserName' => $this->getUserName($this->onlineUserId),
                            'updateTime' => date('Y-m-d H:i:s')
                        ];

                        $result = $this->objMSettlementCycle->updateCycle($cycleData['id'], $updateData);

                        if ($result->isSuccess()) {
                            $this->sendOutput(['message' => '已切换为使用默认配置']);
                        } else {
                            $this->sendOutput($result->getData(), $result->getErrorCode());
                        }
                    } else {
                        // 已经是使用默认配置，无需操作
                        $this->sendOutput(['message' => '已使用默认配置']);
                    }
                } else {
                    $this->sendOutput($cycleResult->getData(), $cycleResult->getErrorCode());
                }

                return;
            }
        } else {
            // 使用自定义配置
            // 必须有供应商ID
            if (empty($supplierId)) {
                $this->sendOutput('供应商ID不能为空', ErrorCode::$paramError);
            }

            // 检查必要字段
            if (!isset($params['cycleType'])) {
                $this->sendOutput('周期类型不能为空', ErrorCode::$paramError);
            }

            // 构建配置数据
            $configData = [
                'supplierId' => $supplierId,
                'cycleType' => intval($params['cycleType']),
                'cycleDay' => isset($params['cycleDay']) ? intval($params['cycleDay']) : 1,
                'cycleMonth' => isset($params['cycleMonth']) ? intval($params['cycleMonth']) : 1,
                'cycleDays' => isset($params['cycleDays']) ? intval($params['cycleDays']) : 7,
                'autoGenerate' => isset($params['autoGenerate']) ? ($params['autoGenerate'] ? true : false) : true,
                'autoApprove' => isset($params['autoApprove']) ? ($params['autoApprove'] ? true : false) : false,
                'minSettlementAmount' => isset($params['minSettlementAmount']) ? floatval($params['minSettlementAmount']) : 0,
                'notificationEnabled' => isset($params['notificationEnabled']) ? ($params['notificationEnabled'] ? true : false) : true,
                'notificationMethods' => isset($params['notificationMethods']) ? $params['notificationMethods'] : ['email', 'system'],
                'status' => isset($params['status']) ? intval($params['status']) : 5, // 默认启用
            ];

            // 查询该供应商是否已有配置
            $cycleResult = $this->objMSettlementCycle->getCycleBySupplier($supplierId);

            if ($cycleResult->isSuccess()) {
                $cycleData = $cycleResult->getData();

                if (!empty($cycleData) && isset($cycleData['id']) && !$cycleData['useDefaultConfig']) {
                    // 已有配置，更新
                    $result = $this->objMSettlementCycle->updateCycle($cycleData['id'], $configData);
                } else {
                    // 没有配置或使用默认配置，新增
                    $result = $this->objMSettlementCycle->addCycle($configData);
                }

                if ($result->isSuccess()) {
                    $this->sendOutput(['message' => '结算周期配置保存成功']);
                } else {
                    $this->sendOutput($result->getData(), $result->getErrorCode());
                }
            } else {
                $this->sendOutput($cycleResult->getData(), $cycleResult->getErrorCode());
            }
        }
    }

    /**
     * 获取用户名
     * @param int $userId 用户ID
     * @return string 用户名
     */
    private function getUserName($userId)
    {
        $objMStaff = new MStaff($this->onlineEnterpriseId);
        $userResult = $objMStaff->getStaffInfoByUserCenterId($userId);
        return $userResult->isSuccess() ? $userResult->getData()['staffName'] : 'User_' . $userId;
    }

    /**
     * 计算下一个结算日期
     */
    public function calculateNextSettlementDate()
    {
        $params = $this->request->getRawJson();
        if (empty($params) || empty($params['cycleType'])) {
            $this->sendOutput('参数不完整', ErrorCode::$paramError);
        }

        $cycle = [
            'cycleType' => intval($params['cycleType']),
            'cycleDays' => isset($params['cycleDays']) ? intval($params['cycleDays']) : 0,
            'cycleDay' => isset($params['cycleDay']) ? intval($params['cycleDay']) : 0
        ];

        $baseDate = isset($params['baseDate']) ? $params['baseDate'] : '';

        $result = $this->objMSettlementCycle->calculateNextSettlementDate($cycle, $baseDate);
        if ($result->isSuccess()) {
            $this->sendOutput($result->getData());
        } else {
            $this->sendOutput($result->getData(), $result->getErrorCode());
        }
    }
}

<?php
/**
 * 结算单控制器类
 * @package JinDouYun\Controller\Finance
 * <AUTHOR>
 * @date 2023/08/16
 */

namespace JinDou<PERSON>un\Controller\Finance;

use <PERSON><PERSON><PERSON><PERSON><PERSON>\Controller\BaseController;
use <PERSON><PERSON><PERSON><PERSON><PERSON>\Model\Finance\MSettlementOrder;
use <PERSON><PERSON><PERSON><PERSON><PERSON>\Model\Finance\MConsignmentSettlementDetail;
use Mall\Framework\Core\ErrorCode;

/**
 * 结算单控制器类
 * @package JinDouYun\Controller\Finance
 */
class SettlementOrder extends BaseController
{
    /**
     * 结算单模型
     * @var MSettlementOrder
     */
    protected $objMSettlementOrder;

    /**
     * 结算单明细模型
     * @var MConsignmentSettlementDetail
     */
    protected $objMSettlementOrderDetails;

    /**
     * 构造函数
     * @param bool $isCheckAcl 是否检查权限
     * @param bool $isMustLogin 是否必须登录
     */
    public function __construct($isCheckAcl = true, $isMustLogin = true)
    {
        parent::__construct($isCheckAcl, $isMustLogin);
        $this->objMSettlementOrder = new MSettlementOrder($this->onlineEnterpriseId, $this->onlineUserId);
        $this->objMSettlementOrderDetails = new MConsignmentSettlementDetail($this->onlineEnterpriseId, $this->onlineUserId);

        // 供应商角色端需要获取供应商ID
        $this->getSupplierIdBySupplierToken();
    }

    /**
     * 获取当前登录供应商ID（供应商角色端专用）
     *
     * @return int 供应商ID
     * @throws \Exception 当供应商未登录或无权限时抛出异常
     */
    protected function getCurrentSupplierId()
    {
        if (empty($this->supplierId)) {
            $this->sendOutput('供应商未登录或无权限', ErrorCode::$notAllowAccess);
        }
        return $this->supplierId;
    }

    /**
     * 验证供应商权限（供应商角色端专用）
     * 确保当前登录的供应商只能访问自己的数据
     *
     * @param int $targetSupplierId 目标供应商ID
     * @return bool 验证通过返回true
     * @throws \Exception 当权限验证失败时抛出异常
     */
    protected function validateSupplierPermission($targetSupplierId)
    {
        $currentSupplierId = $this->getCurrentSupplierId();

        if ($currentSupplierId != $targetSupplierId) {
            $this->sendOutput('无权限访问其他供应商数据', ErrorCode::$notAllowAccess);
        }

        return true;
    }

    /**
     * 公共字段过滤方法
     * @return array 过滤后的参数
     */
    public function commonFieldFilter()
    {
        $params = $this->request->getRawJson();
        if (empty($params)) {
            $this->sendOutput('参数为空', ErrorCode::$paramError);
        }

        $returnData = [];

        // 供应商ID
        if (isset($params['supplierId']) && !empty($params['supplierId'])) {
            $returnData['supplierId'] = intval($params['supplierId']);
        }

        // 供应商名称
        if (isset($params['supplierName']) && !empty($params['supplierName'])) {
            $returnData['supplierName'] = $params['supplierName'];
        }

        // 结算周期开始日期
        if (isset($params['settlementStartDate']) && !empty($params['settlementStartDate'])) {
            $returnData['settlementStartDate'] = $params['settlementStartDate'];
        }

        // 结算周期结束日期
        if (isset($params['settlementEndDate']) && !empty($params['settlementEndDate'])) {
            $returnData['settlementEndDate'] = $params['settlementEndDate'];
        }

        // 结算类型
        if (isset($params['settlementType'])) {
            $returnData['settlementType'] = intval($params['settlementType']);
        }

        // 备注
        if (isset($params['remark'])) {
            $returnData['remark'] = $params['remark'];
        }

        return $returnData;
    }



    /**
     * 获取结算单详情
     */
    public function getSettlementDetail()
    {
        $id = $this->request->param('request_id');
        if (empty($id)) {
            $this->sendOutput('结算单ID不能为空', ErrorCode::$paramError);
        }

        $result = $this->objMSettlementOrder->getSettlementDetail($id);
        if ($result->isSuccess()) {
            $this->sendOutput($result->getData());
        } else {
            $this->sendOutput($result->getData(), $result->getErrorCode());
        }
    }



    /**
     * 获取结算单明细列表
     */
    public function getSettlementDetails()
    {
        $params = $this->request->getRawJson();
        if (empty($params) || (empty($params['settlementId']) && empty($params['settlementNo']))) {
            $this->sendOutput('结算单ID或结算单号不能为空', ErrorCode::$paramError);
        }

        $page = isset($params['page']) ? intval($params['page']) : 1;
        $size = isset($params['pageSize']) ? intval($params['pageSize']) : 20;

        if (!empty($params['settlementId'])) {
            $result = $this->objMSettlementOrderDetails->getDetailsBySettlementId($params['settlementId'], $page, $size);
        } else {
            $result = $this->objMSettlementOrderDetails->getDetailsBySettlementNo($params['settlementNo'], $page, $size);
        }

        if ($result->isSuccess()) {
            $data = $result->getData();
            $pageData = [
                'pageIndex' => $page,
                'pageSize' => $size,
                'pageTotal' => $data['total']
            ];
            $this->sendOutput($data['list'], 0, $pageData);
        } else {
            $this->sendOutput($result->getData(), $result->getErrorCode());
        }
    }

    /**
     * 获取结算单列表
     * API: getSettlementOrders
     */
    public function getSettlementOrders()
    {
        $params = $this->request->getRawJson();
        if (empty($params)) {
            $params = [];
        }

        // 设置分页参数
        $page = pageToOffset($params['page'], $params['pageSize']);

        // 设置查询条件
        $conditions = [];

        // 供应商ID
        if (isset($params['supplierId']) && !empty($params['supplierId'])) {
            $conditions['supplierId'] = intval($params['supplierId']);
        }

        // 结算单号
        if (isset($params['settlementNo']) && !empty($params['settlementNo'])) {
            $conditions['settlementNo'] = $params['settlementNo'];
        }

        // 结算状态
        if (isset($params['settlementStatus']) && !empty($params['settlementStatus'])) {
            $conditions['settlementStatus'] = intval($params['settlementStatus']);
        }

        // 结算类型
        if (isset($params['settlementType']) && !empty($params['settlementType'])) {
            $conditions['settlementType'] = intval($params['settlementType']);
        }

        // 结算时间范围 - 开始
        if (isset($params['startTime']) && !empty($params['startTime'])) {
            $conditions['settlementTime >= '] = $params['startTime'];
        }

        // 结算时间范围 - 结束
        if (isset($params['endTime']) && !empty($params['endTime'])) {
            $conditions['settlementTime <= '] = $params['endTime'];
        }

        $result = $this->objMSettlementOrder->getSettlementList($conditions, $page['offset'], $page['limit']);
        if ($result->isSuccess()) {
            $data = $result->getData();
            $pageData = [
                'pageTotal' => $data['total']
            ];
            $this->sendOutput($data['list'], 0, $pageData);
        } else {
            $this->sendOutput($result->getData(), $result->getErrorCode());
        }
    }

    /**
     * 获取结算单详情
     * API: getSettlementOrderDetail
     */
    public function getSettlementOrderDetail()
    {
        $id = $this->request->param('request_id');
        if (empty($id)) {
            $this->sendOutput('结算单ID不能为空', ErrorCode::$paramError);
        }

        $result = $this->objMSettlementOrder->getSettlementDetail($id);
        if ($result->isSuccess()) {
            $settlementData = $result->getData();

            // 获取结算单明细
            $detailsResult = $this->objMSettlementOrderDetails->getDetailsBySettlementId($id, 1, 1000);
            if ($detailsResult->isSuccess()) {
                $detailsData = $detailsResult->getData();
                $settlementData['details'] = $detailsData['list'];
                $settlementData['detailsTotal'] = $detailsData['total'];
            } else {
                $settlementData['details'] = [];
                $settlementData['detailsTotal'] = 0;
            }

            $this->sendOutput($settlementData);
        } else {
            $this->sendOutput($result->getData(), $result->getErrorCode());
        }
    }

    /**
     * 获取供应商结算流水记录（供应商角色端专用）
     * 任务11：结算流水查询模块 - 后端
     *
     * @return void
     */
    public function getSupplierSettlementRecords()
    {
        try {
            $currentSupplierId = $this->getCurrentSupplierId();
            $params = $this->request->getRawJson();

            // 设置分页参数
            $pageParams = pageToOffset($params['page'], $params['pageSize']);

            // 设置查询条件
            $conditions = [
                'supplierId' => $currentSupplierId
            ];

            // 添加结算月份搜索
            if (!empty($params['settlementMonth'])) {
                $conditions['settlementMonth'] = $params['settlementMonth'];
            }

            // 添加结算状态搜索
            if (!empty($params['settlementStatus'])) {
                $conditions['settlementStatus'] = intval($params['settlementStatus']);
            }

            // 调用模型获取结算流水记录
            $result = $this->objMSettlementOrder->getSupplierSettlementRecords($conditions, $pageParams['offset'], $pageParams['limit']);

            if ($result->isSuccess()) {
                $data = $result->getData();
                $pageData = [
                    'pageTotal' => $data['total'] ?? 0,
                ];
                $this->sendOutput($data['list'] ?? [], 0, $pageData);
            } else {
                $this->sendOutput($result->getData(), $result->getErrorCode());
            }
        } catch (\Exception $e) {
            $this->sendOutput('获取结算流水记录失败：' . $e->getMessage(), ErrorCode::$systemError);
        }
    }

    /**
     * 获取供应商结算汇总数据（供应商角色端专用）
     * 任务11：结算流水查询模块 - 后端
     *
     * @return void
     */
    public function getSupplierSettlementSummary()
    {
        try {
            $currentSupplierId = $this->getCurrentSupplierId();
            $params = $this->request->getRawJson();

            // 设置查询条件
            $conditions = [
                'supplierId' => $currentSupplierId
            ];

            // 添加结算月份搜索
            if (!empty($params['settlementMonth'])) {
                $conditions['settlementMonth'] = $params['settlementMonth'];
            }

            // 调用模型获取汇总数据
            $result = $this->objMSettlementOrder->getSupplierSettlementSummary($conditions);

            if ($result->isSuccess()) {
                $this->sendOutput($result->getData());
            } else {
                $this->sendOutput($result->getData(), $result->getErrorCode());
            }
        } catch (\Exception $e) {
            $this->sendOutput('获取结算汇总数据失败：' . $e->getMessage(), ErrorCode::$systemError);
        }
    }

    /**
     * 预览结算单生成
     * API: previewSettlementGeneration
     *
     * 参数要求：
     * - supplierId: 供应商ID（必填）
     * - settlementStartTime: 结算开始时间Unix时间戳，秒为单位（必填）
     * - settlementEndTime: 结算结束时间Unix时间戳，秒为单位（必填）
     */
    public function previewSettlementGeneration()
    {
        $params = $this->request->getRawJson();
        if (empty($params)) {
            $this->sendOutput('参数为空', ErrorCode::$paramError);
        }

        // 验证必要参数
        if (empty($params['supplierId'])) {
            $this->sendOutput('供应商ID不能为空', ErrorCode::$paramError);
        }

        // 验证时间戳参数
        if (empty($params['settlementStartTime']) || empty($params['settlementEndTime'])) {
            $this->sendOutput('结算时间范围不能为空，请提供settlementStartTime和settlementEndTime时间戳参数', ErrorCode::$paramError);
        }

        // 验证时间戳格式
        if (!$this->validateTimestamp($params['settlementStartTime']) || !$this->validateTimestamp($params['settlementEndTime'])) {
            $this->sendOutput('时间戳格式不正确，必须为有效的Unix时间戳（整数，秒为单位）', ErrorCode::$paramError);
        }

        // 验证时间范围
        if ($params['settlementStartTime'] >= $params['settlementEndTime']) {
            $this->sendOutput('开始时间必须小于结束时间', ErrorCode::$paramError);
        }

        // 转换时间戳为日期字符串（用于模型层处理）
        $params['startDate'] = date('Y-m-d', $params['settlementStartTime']);
        $params['endDate'] = date('Y-m-d', $params['settlementEndTime']);

        // 调用模型预览结算单生成
        $result = $this->objMSettlementOrder->previewSettlementGeneration($params);

        if ($result->isSuccess()) {
            $this->sendOutput($result->getData());
        } else {
            $this->sendOutput($result->getData(), $result->getErrorCode());
        }
    }

    /**
     * 手动生成结算单（通用接口）
     * API: manualGenerateSettlement
     * 复用定时结算的核心业务逻辑，确保手动和定时结算结果一致
     *
     * 参数要求：
     * - supplierId: 供应商ID（必填）
     * - settlementStartTime: 结算开始时间Unix时间戳，秒为单位（必填）
     * - settlementEndTime: 结算结束时间Unix时间戳，秒为单位（必填）
     * - remark: 备注信息（可选）
     */
    public function manualGenerateSettlement()
    {
        try {
            $params = $this->request->getRawJson();
            if (empty($params)) {
                $this->sendOutput('参数为空', ErrorCode::$paramError);
            }

            // 验证必要参数
            if (empty($params['supplierId'])) {
                $this->sendOutput('供应商ID不能为空', ErrorCode::$paramError);
            }

            // 验证时间戳参数
            if (empty($params['settlementStartTime']) || empty($params['settlementEndTime'])) {
                $this->sendOutput('结算时间范围不能为空，请提供settlementStartTime和settlementEndTime时间戳参数', ErrorCode::$paramError);
            }

            // 验证时间戳格式
            if (!$this->validateTimestamp($params['settlementStartTime']) || !$this->validateTimestamp($params['settlementEndTime'])) {
                $this->sendOutput('时间戳格式不正确，必须为有效的Unix时间戳（整数，秒为单位）', ErrorCode::$paramError);
            }

            $settlementStartTime = intval($params['settlementStartTime']);
            $settlementEndTime = intval($params['settlementEndTime']);

            // 验证时间范围
            if ($settlementStartTime >= $settlementEndTime) {
                $this->sendOutput('开始时间必须小于结束时间', ErrorCode::$paramError);
            }

            // 准备结算数据
            $settlementData = [
                'supplierId' => intval($params['supplierId']),
                'settlementStartTime' => $settlementStartTime, // 使用时间戳
                'settlementEndTime' => $settlementEndTime, // 使用时间戳
                'remark' => isset($params['remark']) ? $params['remark'] : '手动生成结算单',
                'createUserName' => $this->onlineUserName ?? '系统',
                'isManual' => true // 标记为手动生成
            ];

            // 调用模型创建结算单，复用定时结算的核心逻辑
            $result = $this->objMSettlementOrder->createSettlementOrder($settlementData);

            if ($result->isSuccess()) {
                $resultData = $result->getData();

                // 返回成功信息
                $this->sendOutput([
                    'success' => true,
                    'message' => '结算单生成成功',
                    'settlementId' => $resultData['settlementId'] ?? null,
                    'settlementNo' => $resultData['settlementNo'] ?? null,
                    'totalAmount' => $resultData['totalAmount'] ?? 0,
                    'detailCount' => $resultData['detailCount'] ?? 0,
                    'settlementStatus' => $resultData['settlementStatus'] ?? null
                ]);
            } else {
                $this->sendOutput($result->getData(), $result->getErrorCode());
            }
        } catch (\Exception $e) {
            $this->sendOutput('生成结算单失败：' . $e->getMessage(), ErrorCode::$systemError);
        }
    }

    /**
     * 审核结算单
     * 审核通过后自动生成应付账单
     */
    public function auditSettlementOrder()
    {
        try {
            $params = $this->request->getRawJson();
            if (empty($params)) {
                $this->sendOutput('参数为空', ErrorCode::$paramError);
            }

            // 验证必要参数
            if (empty($params['settlementId'])) {
                $this->sendOutput('结算单ID不能为空', ErrorCode::$paramError);
            }

            // 调用模型审核结算单
            $result = $this->objMSettlementOrder->auditSettlementOrder($params['settlementId']);

            if ($result->isSuccess()) {
                $this->sendOutput([
                    'success' => true,
                    'message' => '结算单审核成功，已生成应付账单',
                    'data' => $result->getData()
                ]);
            } else {
                $this->sendOutput($result->getData(), $result->getErrorCode());
            }
        } catch (\Exception $e) {
            $this->sendOutput('审核结算单失败：' . $e->getMessage(), ErrorCode::$systemError);
        }
    }

    /**
     * 生成供应商仓储结算单（供应商角色端专用）
     * API: generateSupplierStorageSettlement
     * 基于固定价格计算，自动获取当前登录供应商ID
     */
    public function generateSupplierStorageSettlement()
    {
        try {
            $currentSupplierId = $this->getCurrentSupplierId();
            $params = $this->request->getRawJson();

            if (empty($params)) {
                $this->sendOutput('参数为空', ErrorCode::$paramError);
            }

            // 验证必要参数
            if (empty($params['settlementMonth'])) {
                $this->sendOutput('结算月份不能为空', ErrorCode::$paramError);
            }

            // 验证月份格式 (yyyy-MM)
            if (!preg_match('/^\d{4}-\d{2}$/', $params['settlementMonth'])) {
                $this->sendOutput('结算月份格式不正确，应为 yyyy-MM', ErrorCode::$paramError);
            }

            // 计算月份的开始和结束日期
            $settlementMonth = $params['settlementMonth'];
            $startDate = $settlementMonth . '-01';
            $endDate = date('Y-m-t', strtotime($startDate)); // 获取月份最后一天

            // 转换为时间戳格式
            $settlementStartTime = strtotime($startDate . ' 00:00:00');
            $settlementEndTime = strtotime($endDate . ' 23:59:59');

            // 准备结算数据
            $settlementData = [
                'supplierId' => $currentSupplierId,
                'settlementStartTime' => $settlementStartTime, // 使用时间戳
                'settlementEndTime' => $settlementEndTime, // 使用时间戳
                'remark' => isset($params['remark']) ? $params['remark'] : '手动生成仓储结算单',
                'createUserName' => '供应商系统',
                'isManual' => true, // 标记为手动生成
                'settlementType' => 2 // 手动结算类型
            ];

            // 调用模型创建结算单
            $result = $this->objMSettlementOrder->createSupplierStorageSettlement($settlementData);

            if ($result->isSuccess()) {
                $resultData = $result->getData();

                // 返回成功信息
                $this->sendOutput([
                    'success' => true,
                    'message' => '仓储结算单生成成功',
                    'settlementId' => $resultData['settlementId'] ?? null,
                    'settlementNo' => $resultData['settlementNo'] ?? null,
                    'totalAmount' => $resultData['totalAmount'] ?? 0,
                    'detailCount' => $resultData['detailCount'] ?? 0,
                    'settlementStatus' => $resultData['settlementStatus'] ?? null,
                    'settlementMonth' => $settlementMonth
                ]);
            } else {
                $this->sendOutput($result->getData(), $result->getErrorCode());
            }
        } catch (\Exception $e) {
            $this->sendOutput('生成仓储结算单失败：' . $e->getMessage(), ErrorCode::$systemError);
        }
    }

    /**
     * 批量审核结算单
     * 批量审核多个结算单并生成对应的应付账单
     */
    public function batchAuditSettlementOrders()
    {
        try {
            $params = $this->request->getRawJson();
            if (empty($params)) {
                $this->sendOutput('参数为空', ErrorCode::$paramError);
            }

            // 验证必要参数
            if (empty($params['settlementIds']) || !is_array($params['settlementIds'])) {
                $this->sendOutput('结算单ID列表不能为空', ErrorCode::$paramError);
            }

            // 调用模型批量审核结算单
            $result = $this->objMSettlementOrder->batchAuditSettlementOrders($params['settlementIds']);

            if ($result->isSuccess()) {
                $data = $result->getData();
                $this->sendOutput([
                    'success' => true,
                    'message' => '批量审核完成',
                    'successCount' => $data['successCount'],
                    'failCount' => $data['failCount'],
                    'results' => $data['results']
                ]);
            } else {
                $this->sendOutput($result->getData(), $result->getErrorCode());
            }
        } catch (\Exception $e) {
            $this->sendOutput('批量审核结算单失败：' . $e->getMessage(), ErrorCode::$systemError);
        }
    }

    /**
     * 验证时间戳格式是否正确
     * @param mixed $timestamp - 待验证的时间戳
     * @return bool - 是否为有效的时间戳
     */
    private function validateTimestamp($timestamp)
    {
        // 检查是否为数字
        if (!is_numeric($timestamp)) {
            return false;
        }

        // 转换为整数
        $timestamp = intval($timestamp);

        // 检查时间戳范围（1970年到2100年之间）
        $minTimestamp = 0; // 1970-01-01
        $maxTimestamp = 4102444800; // 2100-01-01

        return $timestamp >= $minTimestamp && $timestamp <= $maxTimestamp;
    }
}

<?php
/**
 * 供应商保证金明细Dao
 * 
 * @copyright   Copyright (c) https://www.qianniaovip.com All rights reserved
 */

namespace JinDouYun\Dao\Purchase;

use <PERSON>DouYun\Dao\BaseDao;

class DSupplierDepositDetail extends BaseDao
{
    public function __construct($serviceDB = 'finance')
    {
        $this->_table = 'supplier_deposit_detail';
        $this->_primary = 'id';
        $this->_fields = [
            'id', // int(11) NOT NULL AUTO_INCREMENT COMMENT '供应商保证金明细表',
            'supplierId', // int(11) NOT NULL COMMENT '供应商id',
            'receiptTime', // int(11) NOT NULL COMMENT '单据日期',
            'no', // char(25) NOT NULL COMMENT '单据编号',
            'beforeAmount', // decimal(12,2) NOT NULL DEFAULT '0.00' COMMENT '变动前金额',
            'amount', // decimal(12,2) NOT NULL DEFAULT '0.00' COMMENT '变动金额',
            'afterAmount', // decimal(12,2) NOT NULL DEFAULT '0.00' COMMENT '变动后金额',
            'operationType', // tinyint(3) NOT NULL COMMENT '操作类型：1充值 2扣除 3冻结 4解冻',
            'status', // tinyint(3) NOT NULL DEFAULT '1' COMMENT '状态：1正常 2已撤销',
            'operatorId', // int(11) NOT NULL COMMENT '操作人ID',
            'operatorName', // varchar(50) NOT NULL COMMENT '操作人姓名',
            'remark', // varchar(255) DEFAULT NULL COMMENT '备注',
            'createTime', // int(11) NOT NULL COMMENT '创建时间',
            'updateTime', // int(11) DEFAULT NULL COMMENT '更新时间',
            'extends', // json DEFAULT NULL COMMENT '扩展字段',
        ];
        $this->_readonly = ['id'];
        $this->_create_autofill = [
            'createTime' => time()
        ];
        $this->_update_autofill = [
            'updateTime' => time()
        ];

        parent::__construct($serviceDB);
    }
}

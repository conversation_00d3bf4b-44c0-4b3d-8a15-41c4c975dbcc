<?php
/**
 * 用户管理Model
 * Created by PhpStorm.
 * User: 小威
 * Date: 2020/03/28
 * Time: 14:33
 */

namespace JinDouYun\Model\Manage;

use Mall\Framework\Core\ErrorCode;
use Mall\Framework\Core\StatusCode;
use Mall\Framework\Core\ResultWrapper;

use <PERSON><PERSON><PERSON><PERSON><PERSON>\Dao\UserCenter\DUserCenter;
use <PERSON><PERSON><PERSON><PERSON>un\Dao\Enterprise\DUserBindEnterprise;

class MUser
{
    /**
     * 用户中心Dao
     * @var DUserCenter
     */
    protected $objDUser;

    /**
     * 用户绑定企业Dao
     * @var DUserBindEnterprise
     */
    protected $objDUserBindEnterprise;

    /**
     * 企业ID
     * @var int
     */
    protected $enterpriseId;

    /**
     * 用户Dao（企业用户表）
     * @var DUserCenter
     */
    protected $objDUserEnterprise;


    /**
     * 构造函数
     *
     * @param int $enterpriseId 企业ID（可选）
     */
    public function __construct($enterpriseId = null)
    {
        $this->objDUser = new DUserCenter('default');
        $this->objDUserBindEnterprise = new DUserBindEnterprise('default');
    }

    /**
     * 用户列表
     * @param $params
     * @return ResultWrapper
     */
    public function getAllUser($params)
    {
        $where = ['isCustomer' => StatusCode::$customerType['user']];
        $dbResult = $this->objDUser->select($where, 'id,mobile,deleteStatus', 'id desc', $params['limit'] , $params['offset']);
        if($dbResult === false){
            return ResultWrapper::fail($this->objDUser->error(), ErrorCode::$dberror);
        }
        $total = $this->objDUser->count($where);

        $returnDta = [
            'total' => $total ? $total : 0,
            'data' => $dbResult,
        ];
        return ResultWrapper::success($returnDta);
    }
}

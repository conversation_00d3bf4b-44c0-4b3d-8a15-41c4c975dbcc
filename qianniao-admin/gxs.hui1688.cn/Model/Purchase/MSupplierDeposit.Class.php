<?php
/**
 * 供应商保证金账户管理模型
 *
 * @copyright   Copyright (c) https://www.qianniaovip.com All rights reserved
 */

namespace JinDouYun\Model\Purchase;

use JinDou<PERSON>un\Common\SupplierDepositType;
use <PERSON><PERSON><PERSON><PERSON><PERSON>\Controller\Common\Logger;
use <PERSON><PERSON><PERSON><PERSON><PERSON>\Dao\Purchase\DSupplier;
use <PERSON><PERSON><PERSON><PERSON><PERSON>\Dao\Purchase\DSupplierDepositDetail;
use <PERSON><PERSON><PERSON><PERSON>un\Dao\Purchase\DSupplierDepositDetailIndex;
use JinDouYun\Model\Department\MStaff;
use Jin<PERSON><PERSON>Yun\Model\Manage\MUser;
use <PERSON>D<PERSON>Y<PERSON>\Model\MBaseModel;
use Mall\Framework\Core\ErrorCode;
use Mall\Framework\Core\ResultWrapper;
use Mall\Framework\Core\StatusCode;

class MSupplierDeposit extends MBaseModel
{
    /**
     * 供应商数据访问对象
     * @var DSupplier
     */
    private $objDSupplier;

    /**
     * 保证金明细数据访问对象
     * @var DSupplierDepositDetail
     */
    private $objDSupplierDepositDetail;

    /**
     * 保证金明细索引数据访问对象
     * @var DSupplierDepositDetailIndex
     */
    private $objDSupplierDepositDetailIndex;

    // 移除未使用的属性

    /**
     * 企业ID
     * @var int
     */
    private $enterpriseId;

    /**
     * 用户ID
     * @var int
     */
    private $userId;

    private $objMUser;

    private $objMStaff;

    /**
     * 构造函数
     *
     * @param int $enterpriseId 企业ID
     * @param int $userId 用户ID
     */
    public function __construct($enterpriseId, $userId)
    {
        parent::__construct($enterpriseId, $userId);
        $this->enterpriseId = $enterpriseId;
        $this->userId = $userId;

        $this->objDSupplier = new DSupplier('stock');
        $this->objDSupplier->setTable('qianniao_supplier_' . $this->enterpriseId);

        $this->objDSupplierDepositDetail = new DSupplierDepositDetail('finance');
        $this->objDSupplierDepositDetail->setTable('qianniao_supplier_deposit_detail_' . $this->enterpriseId);

        $this->objDSupplierDepositDetailIndex = new DSupplierDepositDetailIndex('finance');
        $this->objDSupplierDepositDetailIndex->setTable('qianniao_supplier_deposit_detail_index_' . $this->enterpriseId);

        $this->objMUser = new MUser($enterpriseId);
        $this->objMStaff = new MStaff($enterpriseId, $userId);
    }

    /**
     * 获取供应商保证金账户信息
     *
     * @param int $supplierId 供应商ID
     * @return ResultWrapper 结果包装器
     */
    public function getDepositAccount($supplierId)
    {
        if (empty($supplierId)) {
            return ResultWrapper::fail('供应商ID不能为空', ErrorCode::$paramError);
        }

        $supplier = $this->objDSupplier->get($supplierId, 'id, title, depositAccount, frozenDeposit');
        if ($supplier === false) {
            Logger::logs(E_USER_ERROR, '获取供应商保证金账户信息失败', __CLASS__, __LINE__, $this->objDSupplier->error());
            return ResultWrapper::fail($this->objDSupplier->error(), ErrorCode::$dberror);
        }

        if (empty($supplier)) {
            return ResultWrapper::fail('供应商不存在', ErrorCode::$paramError);
        }

        $result = [
            'id' => $supplier['id'],
            'title' => $supplier['title'],
            'depositAccount' => floatval($supplier['depositAccount']),
            'frozenDeposit' => floatval($supplier['frozenDeposit']),
            'availableDeposit' => floatval($supplier['depositAccount']) - floatval($supplier['frozenDeposit'])
        ];

        return ResultWrapper::success($result);
    }

    /**
     * 保证金充值
     *
     * @param int $supplierId 供应商ID
     * @param float $amount 充值金额
     * @param string $remark 备注
     * @return ResultWrapper 结果包装器
     */
    public function deposit($supplierId, $amount, $remark = '')
    {
        if (empty($supplierId)) {
            return ResultWrapper::fail('供应商ID不能为空', ErrorCode::$paramError);
        }

        if ($amount <= 0) {
            return ResultWrapper::fail('充值金额必须大于0', ErrorCode::$paramError);
        }

        // 开始事务
        $this->objDSupplier->beginTransaction();

        try {
            // 获取供应商信息
            $supplier = $this->objDSupplier->get($supplierId, 'id, title, depositAccount');
            if (empty($supplier)) {
                $this->objDSupplier->rollBack();
                return ResultWrapper::fail('供应商不存在', ErrorCode::$paramError);
            }

            $beforeAmount = floatval($supplier['depositAccount']);
            $afterAmount = $beforeAmount + $amount;

            // 更新供应商保证金账户余额
            $updateResult = $this->objDSupplier->update(['depositAccount' => $afterAmount], $supplierId);
            if ($updateResult === false) {
                $this->objDSupplier->rollBack();
                Logger::logs(E_USER_ERROR, '更新供应商保证金账户余额失败', __CLASS__, __LINE__, $this->objDSupplier->error());
                return ResultWrapper::fail($this->objDSupplier->error(), ErrorCode::$dberror);
            }

            // 生成流水单号
            $no = $this->generateDepositNo(SupplierDepositType::DEPOSIT);

            $result = $this->objMStaff->getStaffInfoByUserCenterId($this->userId);
            if ($result->isSuccess()) {
                $userName = $result->getData()['staffName'];
            }

            // 添加保证金流水记录
            $detailData = [
                'supplierId' => $supplierId,
                'receiptTime' => time(),
                'no' => $no,
                'beforeAmount' => $beforeAmount,
                'amount' => $amount,
                'afterAmount' => $afterAmount,
                'operationType' => SupplierDepositType::DEPOSIT,
                'status' => StatusCode::$standard,
                'operatorId' => $this->userId,
                'operatorName' => empty($userName) ? '' : $userName,
                'remark' => $remark,
                'createTime' => time(),
                'updateTime' => time()
            ];

            $detailId = $this->objDSupplierDepositDetail->insert($detailData);
            if ($detailId === false) {
                $this->objDSupplier->rollBack();
                Logger::logs(E_USER_ERROR, '添加保证金流水记录失败', __CLASS__, __LINE__, $this->objDSupplierDepositDetail->error());
                return ResultWrapper::fail($this->objDSupplierDepositDetail->error(), ErrorCode::$dberror);
            }

            // 添加保证金流水索引
            $indexData = [
                'detailId' => $detailId,
                'supplierId' => $supplierId,
                'receiptTime' => time(),
                'no' => $no,
                'operationType' => SupplierDepositType::DEPOSIT,
                'createTime' => time(),
                'updateTime' => time()
            ];

            $indexId = $this->objDSupplierDepositDetailIndex->insert($indexData);
            if ($indexId === false) {
                $this->objDSupplier->rollBack();
                Logger::logs(E_USER_ERROR, '添加保证金流水索引失败', __CLASS__, __LINE__, $this->objDSupplierDepositDetailIndex->error());
                return ResultWrapper::fail($this->objDSupplierDepositDetailIndex->error(), ErrorCode::$dberror);
            }

            // 提交事务
            $this->objDSupplier->commit();

            // 记录操作日志
            Logger::logs(E_USER_NOTICE, '供应商保证金充值', __CLASS__, __LINE__, [
                'supplierId' => $supplierId,
                'amount' => $amount,
                'operationType' => SupplierDepositType::DEPOSIT,
                'remark' => $remark,
                'userId' => $this->userId
            ]);

            $result = [
                'id' => $detailId,
                'no' => $no,
                'supplierId' => $supplierId,
                'supplierName' => $supplier['title'],
                'amount' => $amount,
                'beforeAmount' => $beforeAmount,
                'afterAmount' => $afterAmount,
                'operationType' => SupplierDepositType::DEPOSIT,
                'operationTypeName' => SupplierDepositType::getTypeName(SupplierDepositType::DEPOSIT)
            ];

            return ResultWrapper::success($result);
        } catch (\Exception $e) {
            $this->objDSupplier->rollBack();
            Logger::logs(E_USER_ERROR, '保证金充值异常', __CLASS__, __LINE__, $e->getMessage());
            return ResultWrapper::fail('保证金充值失败：' . $e->getMessage(), ErrorCode::$systemError);
        }
    }

    /**
     * 保证金扣除
     *
     * @param int $supplierId 供应商ID
     * @param float $amount 扣除金额
     * @param string $remark 备注
     * @return ResultWrapper 结果包装器
     */
    public function deduct($supplierId, $amount, $remark = '')
    {
        if (empty($supplierId)) {
            return ResultWrapper::fail('供应商ID不能为空', ErrorCode::$paramError);
        }

        if ($amount <= 0) {
            return ResultWrapper::fail('扣除金额必须大于0', ErrorCode::$paramError);
        }

        // 开始事务
        $this->objDSupplier->beginTransaction();

        try {
            // 获取供应商信息
            $supplier = $this->objDSupplier->get($supplierId, 'id, title, depositAccount, frozenDeposit');
            if (empty($supplier)) {
                $this->objDSupplier->rollBack();
                return ResultWrapper::fail('供应商不存在', ErrorCode::$paramError);
            }

            $beforeAmount = floatval($supplier['depositAccount']);
            $frozenDeposit = floatval($supplier['frozenDeposit']);
            $availableAmount = $beforeAmount - $frozenDeposit;

            // 检查可用余额是否足够
            if ($availableAmount < $amount) {
                $this->objDSupplier->rollBack();
                return ResultWrapper::fail('可用保证金余额不足', ErrorCode::$paramError);
            }

            $afterAmount = $beforeAmount - $amount;

            // 更新供应商保证金账户余额
            $updateResult = $this->objDSupplier->update(['depositAccount' => $afterAmount], $supplierId);
            if ($updateResult === false) {
                $this->objDSupplier->rollBack();
                Logger::logs(E_USER_ERROR, '更新供应商保证金账户余额失败', __CLASS__, __LINE__, $this->objDSupplier->error());
                return ResultWrapper::fail($this->objDSupplier->error(), ErrorCode::$dberror);
            }

            // 生成流水单号
            $no = $this->generateDepositNo(SupplierDepositType::DEDUCT);

            $result = $this->objMStaff->getStaffInfoByUserCenterId($this->userId);            
            if ($result->isSuccess()) {
                $userName = $result->getData()['staffName'];
            }

            // 添加保证金流水记录
            $detailData = [
                'supplierId' => $supplierId,
                'receiptTime' => time(),
                'no' => $no,
                'beforeAmount' => $beforeAmount,
                'amount' => -$amount, // 扣除为负数
                'afterAmount' => $afterAmount,
                'operationType' => SupplierDepositType::DEDUCT,
                'status' => StatusCode::$standard,
                'operatorId' => $this->userId,
                'operatorName' => empty($userName) ? '' : $userName,
                'remark' => $remark,
                'createTime' => time(),
                'updateTime' => time()
            ];

            $detailId = $this->objDSupplierDepositDetail->insert($detailData);
            if ($detailId === false) {
                $this->objDSupplier->rollBack();
                Logger::logs(E_USER_ERROR, '添加保证金流水记录失败', __CLASS__, __LINE__, $this->objDSupplierDepositDetail->error());
                return ResultWrapper::fail($this->objDSupplierDepositDetail->error(), ErrorCode::$dberror);
            }

            // 添加保证金流水索引
            $indexData = [
                'detailId' => $detailId,
                'supplierId' => $supplierId,
                'receiptTime' => time(),
                'no' => $no,
                'operationType' => SupplierDepositType::DEDUCT,
                'createTime' => time(),
                'updateTime' => time()
            ];

            $indexId = $this->objDSupplierDepositDetailIndex->insert($indexData);
            if ($indexId === false) {
                $this->objDSupplier->rollBack();
                Logger::logs(E_USER_ERROR, '添加保证金流水索引失败', __CLASS__, __LINE__, $this->objDSupplierDepositDetailIndex->error());
                return ResultWrapper::fail($this->objDSupplierDepositDetailIndex->error(), ErrorCode::$dberror);
            }

            // 提交事务
            $this->objDSupplier->commit();

            // 记录操作日志
            Logger::logs(E_USER_NOTICE, '供应商保证金扣除', __CLASS__, __LINE__, [
                'supplierId' => $supplierId,
                'amount' => $amount,
                'operationType' => SupplierDepositType::DEDUCT,
                'remark' => $remark,
                'userId' => $this->userId
            ]);

            $result = [
                'id' => $detailId,
                'no' => $no,
                'supplierId' => $supplierId,
                'supplierName' => $supplier['title'],
                'amount' => -$amount,
                'beforeAmount' => $beforeAmount,
                'afterAmount' => $afterAmount,
                'operationType' => SupplierDepositType::DEDUCT,
                'operationTypeName' => SupplierDepositType::getTypeName(SupplierDepositType::DEDUCT)
            ];

            return ResultWrapper::success($result);
        } catch (\Exception $e) {
            $this->objDSupplier->rollBack();
            Logger::logs(E_USER_ERROR, '保证金扣除异常', __CLASS__, __LINE__, $e->getMessage());
            return ResultWrapper::fail('保证金扣除失败：' . $e->getMessage(), ErrorCode::$systemError);
        }
    }

    /**
     * 保证金冻结
     *
     * @param int $supplierId 供应商ID
     * @param float $amount 冻结金额
     * @param string $remark 备注
     * @return ResultWrapper 结果包装器
     */
    public function freeze($supplierId, $amount, $remark = '')
    {
        if (empty($supplierId)) {
            return ResultWrapper::fail('供应商ID不能为空', ErrorCode::$paramError);
        }

        if ($amount <= 0) {
            return ResultWrapper::fail('冻结金额必须大于0', ErrorCode::$paramError);
        }

        // 开始事务
        $this->objDSupplier->beginTransaction();

        try {
            // 获取供应商信息
            $supplier = $this->objDSupplier->get($supplierId, 'id, title, depositAccount, frozenDeposit');
            if (empty($supplier)) {
                $this->objDSupplier->rollBack();
                return ResultWrapper::fail('供应商不存在', ErrorCode::$paramError);
            }

            $depositAccount = floatval($supplier['depositAccount']);
            $beforeFrozen = floatval($supplier['frozenDeposit']);
            $availableAmount = $depositAccount - $beforeFrozen;

            // 检查可用余额是否足够
            if ($availableAmount < $amount) {
                $this->objDSupplier->rollBack();
                return ResultWrapper::fail('可用保证金余额不足', ErrorCode::$paramError);
            }

            $afterFrozen = $beforeFrozen + $amount;

            // 更新供应商冻结保证金金额
            $updateResult = $this->objDSupplier->update(['frozenDeposit' => $afterFrozen], $supplierId);
            if ($updateResult === false) {
                $this->objDSupplier->rollBack();
                Logger::logs(E_USER_ERROR, '更新供应商冻结保证金金额失败', __CLASS__, __LINE__, $this->objDSupplier->error());
                return ResultWrapper::fail($this->objDSupplier->error(), ErrorCode::$dberror);
            }

            // 生成流水单号
            $no = $this->generateDepositNo(SupplierDepositType::FREEZE);

            $result = $this->objMStaff->getStaffInfoByUserCenterId($this->userId);
            if ($result->isSuccess()) {
                $userName = $result->getData()['staffName'];
            }

            // 添加保证金流水记录
            $detailData = [
                'supplierId' => $supplierId,
                'receiptTime' => time(),
                'no' => $no,
                'beforeAmount' => $beforeFrozen,
                'amount' => $amount,
                'afterAmount' => $afterFrozen,
                'operationType' => SupplierDepositType::FREEZE,
                'status' => StatusCode::$standard,
                'operatorId' => $this->userId,
                'operatorName' => empty($userName) ? '' : $userName,
                'remark' => $remark,
                'createTime' => time(),
                'updateTime' => time(),
                'extends' => json_encode(['depositAccount' => $depositAccount])
            ];

            $detailId = $this->objDSupplierDepositDetail->insert($detailData);
            if ($detailId === false) {
                $this->objDSupplier->rollBack();
                Logger::logs(E_USER_ERROR, '添加保证金流水记录失败', __CLASS__, __LINE__, $this->objDSupplierDepositDetail->error());
                return ResultWrapper::fail($this->objDSupplierDepositDetail->error(), ErrorCode::$dberror);
            }

            // 添加保证金流水索引
            $indexData = [
                'detailId' => $detailId,
                'supplierId' => $supplierId,
                'receiptTime' => time(),
                'no' => $no,
                'operationType' => SupplierDepositType::FREEZE,
                'createTime' => time(),
                'updateTime' => time()
            ];

            $indexId = $this->objDSupplierDepositDetailIndex->insert($indexData);
            if ($indexId === false) {
                $this->objDSupplier->rollBack();
                Logger::logs(E_USER_ERROR, '添加保证金流水索引失败', __CLASS__, __LINE__, $this->objDSupplierDepositDetailIndex->error());
                return ResultWrapper::fail($this->objDSupplierDepositDetailIndex->error(), ErrorCode::$dberror);
            }

            // 提交事务
            $this->objDSupplier->commit();

            // 记录操作日志
            Logger::logs(E_USER_NOTICE, '供应商保证金冻结', __CLASS__, __LINE__, [
                'supplierId' => $supplierId,
                'amount' => $amount,
                'operationType' => SupplierDepositType::FREEZE,
                'remark' => $remark,
                'userId' => $this->userId
            ]);

            $result = [
                'id' => $detailId,
                'no' => $no,
                'supplierId' => $supplierId,
                'supplierName' => $supplier['title'],
                'amount' => $amount,
                'beforeFrozen' => $beforeFrozen,
                'afterFrozen' => $afterFrozen,
                'depositAccount' => $depositAccount,
                'availableAmount' => $depositAccount - $afterFrozen,
                'operationType' => SupplierDepositType::FREEZE,
                'operationTypeName' => SupplierDepositType::getTypeName(SupplierDepositType::FREEZE)
            ];

            return ResultWrapper::success($result);
        } catch (\Exception $e) {
            $this->objDSupplier->rollBack();
            Logger::logs(E_USER_ERROR, '保证金冻结异常', __CLASS__, __LINE__, $e->getMessage());
            return ResultWrapper::fail('保证金冻结失败：' . $e->getMessage(), ErrorCode::$systemError);
        }
    }

    /**
     * 保证金提取（从账户中提取资金）
     *
     * @param int $supplierId 供应商ID
     * @param float $amount 提取金额
     * @param string $remark 备注
     * @return ResultWrapper 结果包装器
     */
    public function withdrawDeposit($supplierId, $amount, $remark = '')
    {
        // 提取保证金本质上是从账户中扣除资金，调用deduct方法
        return $this->deduct($supplierId, $amount, $remark);
    }

    /**
     * 保证金解冻
     *
     * @param int $supplierId 供应商ID
     * @param float $amount 解冻金额
     * @param string $remark 备注
     * @return ResultWrapper 结果包装器
     */
    public function unfreeze($supplierId, $amount, $remark = '')
    {
        if (empty($supplierId)) {
            return ResultWrapper::fail('供应商ID不能为空', ErrorCode::$paramError);
        }

        if ($amount <= 0) {
            return ResultWrapper::fail('解冻金额必须大于0', ErrorCode::$paramError);
        }

        // 开始事务
        $this->objDSupplier->beginTransaction();

        try {
            // 获取供应商信息
            $supplier = $this->objDSupplier->get($supplierId, 'id, title, depositAccount, frozenDeposit');
            if (empty($supplier)) {
                $this->objDSupplier->rollBack();
                return ResultWrapper::fail('供应商不存在', ErrorCode::$paramError);
            }

            $depositAccount = floatval($supplier['depositAccount']);
            $beforeFrozen = floatval($supplier['frozenDeposit']);

            // 检查冻结金额是否足够
            if ($beforeFrozen < $amount) {
                $this->objDSupplier->rollBack();
                return ResultWrapper::fail('冻结保证金金额不足', ErrorCode::$paramError);
            }

            $afterFrozen = $beforeFrozen - $amount;

            // 更新供应商冻结保证金金额
            $updateResult = $this->objDSupplier->update(['frozenDeposit' => $afterFrozen], $supplierId);
            if ($updateResult === false) {
                $this->objDSupplier->rollBack();
                Logger::logs(E_USER_ERROR, '更新供应商冻结保证金金额失败', __CLASS__, __LINE__, $this->objDSupplier->error());
                return ResultWrapper::fail($this->objDSupplier->error(), ErrorCode::$dberror);
            }

            // 生成流水单号
            $no = $this->generateDepositNo(SupplierDepositType::UNFREEZE);

            $result = $this->objMStaff->getStaffInfoByUserCenterId($this->userId);
            if ($result->isSuccess()) {
                $userName = $result->getData()['staffName'];
            }

            // 添加保证金流水记录
            $detailData = [
                'supplierId' => $supplierId,
                'receiptTime' => time(),
                'no' => $no,
                'beforeAmount' => $beforeFrozen,
                'amount' => -$amount, // 解冻为负数
                'afterAmount' => $afterFrozen,
                'operationType' => SupplierDepositType::UNFREEZE,
                'status' => StatusCode::$standard,
                'operatorId' => $this->userId,
                'operatorName' => empty($userName) ? '' : $userName,
                'remark' => $remark,
                'createTime' => time(),
                'updateTime' => time(),
                'extends' => json_encode(['depositAccount' => $depositAccount])
            ];

            $detailId = $this->objDSupplierDepositDetail->insert($detailData);
            if ($detailId === false) {
                $this->objDSupplier->rollBack();
                Logger::logs(E_USER_ERROR, '添加保证金流水记录失败', __CLASS__, __LINE__, $this->objDSupplierDepositDetail->error());
                return ResultWrapper::fail($this->objDSupplierDepositDetail->error(), ErrorCode::$dberror);
            }

            // 添加保证金流水索引
            $indexData = [
                'detailId' => $detailId,
                'supplierId' => $supplierId,
                'receiptTime' => time(),
                'no' => $no,
                'operationType' => SupplierDepositType::UNFREEZE,
                'createTime' => time(),
                'updateTime' => time()
            ];

            $indexId = $this->objDSupplierDepositDetailIndex->insert($indexData);
            if ($indexId === false) {
                $this->objDSupplier->rollBack();
                Logger::logs(E_USER_ERROR, '添加保证金流水索引失败', __CLASS__, __LINE__, $this->objDSupplierDepositDetailIndex->error());
                return ResultWrapper::fail($this->objDSupplierDepositDetailIndex->error(), ErrorCode::$dberror);
            }

            // 提交事务
            $this->objDSupplier->commit();

            // 记录操作日志
            Logger::logs(E_USER_NOTICE, '供应商保证金解冻', __CLASS__, __LINE__, [
                'supplierId' => $supplierId,
                'amount' => $amount,
                'operationType' => SupplierDepositType::UNFREEZE,
                'remark' => $remark,
                'userId' => $this->userId
            ]);

            $result = [
                'id' => $detailId,
                'no' => $no,
                'supplierId' => $supplierId,
                'supplierName' => $supplier['title'],
                'amount' => -$amount,
                'beforeFrozen' => $beforeFrozen,
                'afterFrozen' => $afterFrozen,
                'depositAccount' => $depositAccount,
                'availableAmount' => $depositAccount - $afterFrozen,
                'operationType' => SupplierDepositType::UNFREEZE,
                'operationTypeName' => SupplierDepositType::getTypeName(SupplierDepositType::UNFREEZE)
            ];

            return ResultWrapper::success($result);
        } catch (\Exception $e) {
            $this->objDSupplier->rollBack();
            Logger::logs(E_USER_ERROR, '保证金解冻异常', __CLASS__, __LINE__, $e->getMessage());
            return ResultWrapper::fail('保证金解冻失败：' . $e->getMessage(), ErrorCode::$systemError);
        }
    }

    /**
     * 查询保证金流水记录
     *
     * @param array $params 查询参数
     * @return ResultWrapper 结果包装器
     */
    public function getDepositDetails($params)
    {
        $supplierId = isset($params['supplierId']) ? intval($params['supplierId']) : 0;
        $operationType = isset($params['operationType']) ? intval($params['operationType']) : 0;
        $startTime = isset($params['startTime']) ? intval($params['startTime']) : 0;
        $endTime = isset($params['endTime']) ? intval($params['endTime']) : 0;
        $page = isset($params['page']) ? intval($params['page']) : 1;
        $pageSize = isset($params['pageSize']) ? intval($params['pageSize']) : 10;

        if (empty($supplierId)) {
            return ResultWrapper::fail('供应商ID不能为空', ErrorCode::$paramError);
        }

        $where = ['supplierId' => $supplierId];

        if ($operationType > 0) {
            $where['operationType'] = $operationType;
        }

        if ($startTime > 0 && $endTime > 0) {
            $where[] = "receiptTime >= $startTime AND receiptTime <= $endTime";
        } elseif ($startTime > 0) {
            $where[] = "receiptTime >= $startTime";
        } elseif ($endTime > 0) {
            $where[] = "receiptTime <= $endTime";
        }

        // 计算总数
        $total = $this->objDSupplierDepositDetailIndex->count($where);

        // 分页查询
        $offset = ($page - 1) * $pageSize;
        $limit = $pageSize;

        $indexList = $this->objDSupplierDepositDetailIndex->select($where, '*', 'createTime DESC', $limit, $offset);
        if ($indexList === false) {
            Logger::logs(E_USER_ERROR, '查询保证金流水索引失败', __CLASS__, __LINE__, $this->objDSupplierDepositDetailIndex->error());
            return ResultWrapper::fail($this->objDSupplierDepositDetailIndex->error(), ErrorCode::$dberror);
        }

        $detailIds = [];
        foreach ($indexList as $index) {
            $detailIds[] = $index['detailId'];
        }

        $details = [];
        if (!empty($detailIds)) {
            $details = $this->objDSupplierDepositDetail->select($detailIds, '*', 'createTime DESC');
            if ($details === false) {
                Logger::logs(E_USER_ERROR, '查询保证金流水记录失败', __CLASS__, __LINE__, $this->objDSupplierDepositDetail->error());
                return ResultWrapper::fail($this->objDSupplierDepositDetail->error(), ErrorCode::$dberror);
            }

            // 格式化数据
            foreach ($details as &$detail) {
                $detail['operationTypeName'] = SupplierDepositType::getTypeName($detail['operationType']);
                $detail['receiptTimeFormat'] = date('Y-m-d H:i:s', $detail['receiptTime']);
                $detail['createTimeFormat'] = date('Y-m-d H:i:s', $detail['createTime']);

                if (!empty($detail['extends'])) {
                    $extends = json_decode($detail['extends'], true);
                    if (is_array($extends)) {
                        $detail = array_merge($detail, $extends);
                    }
                }
            }
        }

        // 获取供应商信息
        $supplier = $this->objDSupplier->get($supplierId, 'id, title, depositAccount, frozenDeposit');
        if ($supplier === false) {
            Logger::logs(E_USER_ERROR, '获取供应商信息失败', __CLASS__, __LINE__, $this->objDSupplier->error());
            return ResultWrapper::fail($this->objDSupplier->error(), ErrorCode::$dberror);
        }

        $result = [
            'list' => $details,
            'total' => $total,
            'page' => $page,
            'pageSize' => $pageSize,
            'supplier' => [
                'id' => $supplier['id'],
                'title' => $supplier['title'],
                'depositAccount' => floatval($supplier['depositAccount']),
                'frozenDeposit' => floatval($supplier['frozenDeposit']),
                'availableDeposit' => floatval($supplier['depositAccount']) - floatval($supplier['frozenDeposit'])
            ]
        ];

        return ResultWrapper::success($result);
    }

    /**
     * 获取保证金操作历史记录
     * 实现任务10.1.6 getDepositOperationHistory
     *
     * @param array $params 查询参数
     * @return ResultWrapper 结果包装器
     */
    public function getDepositOperationHistory($params)
    {
        $supplierId = isset($params['supplierId']) ? intval($params['supplierId']) : 0;
        $operationTypes = isset($params['operationTypes']) ? $params['operationTypes'] : [];
        $startTime = isset($params['startTime']) ? intval($params['startTime']) : 0;
        $endTime = isset($params['endTime']) ? intval($params['endTime']) : 0;
        $page = isset($params['page']) ? intval($params['page']) : 1;
        $pageSize = isset($params['pageSize']) ? intval($params['pageSize']) : 10;

        if (empty($supplierId)) {
            return ResultWrapper::fail('供应商ID不能为空', ErrorCode::$paramError);
        }

        $where = ['supplierId' => $supplierId];

        // 筛选操作类型
        if (!empty($operationTypes)) {
            $where['operationType'] = $operationTypes;
        }

        // 筛选时间范围
        if ($startTime > 0 && $endTime > 0) {
            $where[] = "receiptTime >= $startTime AND receiptTime <= $endTime";
        } elseif ($startTime > 0) {
            $where[] = "receiptTime >= $startTime";
        } elseif ($endTime > 0) {
            $where[] = "receiptTime <= $endTime";
        }



        // 计算总数
        $total = $this->objDSupplierDepositDetail->count($where);

        // 分页查询
        $offset = ($page - 1) * $pageSize;
        $limit = $pageSize;

        $details = $this->objDSupplierDepositDetail->select($where, '*', 'createTime DESC', $limit, $offset);
        if ($details === false) {
            Logger::logs(E_USER_ERROR, '查询保证金操作历史记录失败', __CLASS__, __LINE__, $this->objDSupplierDepositDetail->error());
            return ResultWrapper::fail($this->objDSupplierDepositDetail->error(), ErrorCode::$dberror);
        }

        // 格式化数据
        foreach ($details as &$detail) {
            $detail['operationTypeName'] = SupplierDepositType::getTypeName($detail['operationType']);
            $detail['receiptTimeFormat'] = date('Y-m-d H:i:s', $detail['receiptTime']);
            $detail['createTimeFormat'] = date('Y-m-d H:i:s', $detail['createTime']);

            if (!empty($detail['extends'])) {
                $extends = json_decode($detail['extends'], true);
                if (is_array($extends)) {
                    $detail = array_merge($detail, $extends);
                }
            }
        }

        // 获取供应商信息
        $supplier = $this->objDSupplier->get($supplierId, 'id, title, depositAccount, frozenDeposit');
        if ($supplier === false) {
            Logger::logs(E_USER_ERROR, '获取供应商信息失败', __CLASS__, __LINE__, $this->objDSupplier->error());
            return ResultWrapper::fail($this->objDSupplier->error(), ErrorCode::$dberror);
        }

        $result = [
            'list' => $details,
            'total' => $total,
            'page' => $page,
            'pageSize' => $pageSize,
            'supplier' => [
                'id' => $supplier['id'],
                'title' => $supplier['title'],
                'depositAccount' => floatval($supplier['depositAccount']),
                'frozenDeposit' => floatval($supplier['frozenDeposit']),
                'availableDeposit' => floatval($supplier['depositAccount']) - floatval($supplier['frozenDeposit'])
            ]
        ];

        return ResultWrapper::success($result);
    }

    /**
     * 生成保证金流水单号
     *
     * @param int $operationType 操作类型
     * @return string 流水单号
     */
    private function generateDepositNo($operationType)
    {
        $prefix = 'DP';
        switch ($operationType) {
            case SupplierDepositType::DEPOSIT:
                $prefix = 'DPC';
                break;
            case SupplierDepositType::DEDUCT:
                $prefix = 'DPD';
                break;
            case SupplierDepositType::FREEZE:
                $prefix = 'DPF';
                break;
            case SupplierDepositType::UNFREEZE:
                $prefix = 'DPU';
                break;
        }

        return $prefix . date('YmdHis') . mt_rand(1000, 9999);
    }
}

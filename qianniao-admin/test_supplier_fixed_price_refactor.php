<?php
/**
 * 测试供应商固定价格列表重构
 * 验证从 consignmentConfig 改为从分账规则表查询的功能
 */

require_once __DIR__ . '/vendor/autoload.php';

use <PERSON><PERSON>ou<PERSON>un\Model\Purchase\MSupplierConsignment;
use <PERSON><PERSON><PERSON><PERSON><PERSON>\Dao\Consignment\DConsignmentRule;

// 测试配置
$enterpriseId = 1; // 测试企业ID
$userId = 1; // 测试用户ID
$supplierId = 1; // 测试供应商ID

echo "=== 供应商固定价格列表重构测试 ===\n\n";

try {
    // 初始化模型
    $model = new MSupplierConsignment($enterpriseId, $userId);
    
    // 测试分页参数
    $page = [
        'offset' => 0,
        'limit' => 10
    ];
    
    // 测试搜索参数
    $searchParams = [
        'keyword' => '',
        'status' => ''
    ];
    
    echo "1. 测试基本查询功能...\n";
    $result = $model->getSupplierFixedPriceList($supplierId, $page, $searchParams);
    
    if ($result->isSuccess()) {
        $data = $result->getData();
        echo "✓ 查询成功\n";
        echo "  - 总记录数: " . ($data['total'] ?? 0) . "\n";
        echo "  - 返回记录数: " . count($data['list'] ?? []) . "\n";
        
        if (!empty($data['list'])) {
            echo "  - 第一条记录示例:\n";
            $firstRecord = $data['list'][0];
            foreach ($firstRecord as $key => $value) {
                if (is_array($value)) {
                    $value = json_encode($value, JSON_UNESCAPED_UNICODE);
                }
                echo "    $key: $value\n";
            }
        }
    } else {
        echo "✗ 查询失败: " . $result->getData() . "\n";
    }
    
    echo "\n2. 测试关键词搜索功能...\n";
    $searchParams['keyword'] = 'iPhone';
    $result = $model->getSupplierFixedPriceList($supplierId, $page, $searchParams);
    
    if ($result->isSuccess()) {
        $data = $result->getData();
        echo "✓ 关键词搜索成功\n";
        echo "  - 搜索关键词: iPhone\n";
        echo "  - 匹配记录数: " . count($data['list'] ?? []) . "\n";
    } else {
        echo "✗ 关键词搜索失败: " . $result->getData() . "\n";
    }
    
    echo "\n3. 测试状态筛选功能...\n";
    $searchParams = [
        'keyword' => '',
        'status' => 'active'
    ];
    $result = $model->getSupplierFixedPriceList($supplierId, $page, $searchParams);
    
    if ($result->isSuccess()) {
        $data = $result->getData();
        echo "✓ 状态筛选成功\n";
        echo "  - 筛选状态: active\n";
        echo "  - 匹配记录数: " . count($data['list'] ?? []) . "\n";
    } else {
        echo "✗ 状态筛选失败: " . $result->getData() . "\n";
    }
    
    echo "\n4. 测试分页功能...\n";
    $page = [
        'offset' => 1,
        'limit' => 5
    ];
    $searchParams = [
        'keyword' => '',
        'status' => ''
    ];
    $result = $model->getSupplierFixedPriceList($supplierId, $page, $searchParams);
    
    if ($result->isSuccess()) {
        $data = $result->getData();
        echo "✓ 分页查询成功\n";
        echo "  - 页码: 2 (offset=1, limit=5)\n";
        echo "  - 返回记录数: " . count($data['list'] ?? []) . "\n";
    } else {
        echo "✗ 分页查询失败: " . $result->getData() . "\n";
    }
    
    echo "\n5. 测试参数验证...\n";
    $result = $model->getSupplierFixedPriceList(null, $page, $searchParams);
    
    if (!$result->isSuccess()) {
        echo "✓ 参数验证正常\n";
        echo "  - 错误信息: " . $result->getData() . "\n";
    } else {
        echo "✗ 参数验证失败，应该返回错误\n";
    }
    
} catch (Exception $e) {
    echo "✗ 测试异常: " . $e->getMessage() . "\n";
    echo "  - 文件: " . $e->getFile() . "\n";
    echo "  - 行号: " . $e->getLine() . "\n";
}

echo "\n=== 测试完成 ===\n";

-- 保证金流水表
CREATE TABLE IF NOT EXISTS `qianniao_supplier_deposit_detail_#enterpriseId#` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '供应商保证金明细表',
  `supplierId` int(11) NOT NULL COMMENT '供应商id',
  `receiptTime` int(11) NOT NULL COMMENT '单据日期',
  `no` char(25) NOT NULL COMMENT '单据编号',
  `beforeAmount` decimal(12,2) NOT NULL DEFAULT '0.00' COMMENT '变动前金额',
  `amount` decimal(12,2) NOT NULL DEFAULT '0.00' COMMENT '变动金额',
  `afterAmount` decimal(12,2) NOT NULL DEFAULT '0.00' COMMENT '变动后金额',
  `operationType` tinyint(3) NOT NULL COMMENT '操作类型：1充值 2扣除 3冻结 4解冻',
  `status` tinyint(3) NOT NULL DEFAULT '1' COMMENT '状态：1正常 2已撤销',
  `operatorId` int(11) NOT NULL COMMENT '操作人ID',
  `operatorName` varchar(50) NOT NULL COMMENT '操作人姓名',
  `remark` varchar(255) DEFAULT NULL COMMENT '备注',
  `createTime` int(11) NOT NULL COMMENT '创建时间',
  `updateTime` int(11) DEFAULT NULL COMMENT '更新时间',
  `extends` json DEFAULT NULL COMMENT '扩展字段',
  PRIMARY KEY (`id`),
  KEY `idx_supplier_id` (`supplierId`),
  KEY `idx_receipt_time` (`receiptTime`),
  KEY `idx_operation_type` (`operationType`),
  KEY `idx_source_id` (`sourceId`),
  KEY `idx_create_time` (`createTime`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='供应商保证金明细表';

-- 保证金流水索引表
CREATE TABLE IF NOT EXISTS `qianniao_supplier_deposit_detail_index_#enterpriseId#` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '供应商保证金明细索引表',
  `detailId` int(11) NOT NULL COMMENT '保证金明细表id',
  `supplierId` int(11) NOT NULL COMMENT '供应商id',
  `receiptTime` int(11) NOT NULL COMMENT '单据日期',
  `no` char(25) NOT NULL COMMENT '单据编号',
  `operationType` tinyint(3) NOT NULL COMMENT '操作类型：1充值 2扣除 3冻结 4解冻',
  `createTime` int(11) NOT NULL COMMENT '创建时间',
  `updateTime` int(11) DEFAULT NULL COMMENT '更新时间',
  `extends` json DEFAULT NULL COMMENT '扩展字段',
  PRIMARY KEY (`id`),
  KEY `idx_supplier_id` (`supplierId`),
  KEY `idx_receipt_time` (`receiptTime`),
  KEY `idx_operation_type` (`operationType`),
  KEY `idx_create_time` (`createTime`),
  KEY `idx_detail_id` (`detailId`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='供应商保证金明细索引表';

-- 确保supplier表有保证金账户字段
ALTER TABLE `supplier_#enterpriseId#` 
ADD COLUMN IF NOT EXISTS `depositAccount` decimal(12,2) DEFAULT '0.00' COMMENT '保证金账户余额',
ADD COLUMN IF NOT EXISTS `frozenDeposit` decimal(12,2) DEFAULT '0.00' COMMENT '冻结的保证金金额';

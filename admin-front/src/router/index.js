/**

 * @description router全局配置，如有必要可分文件抽离，其中asyncRoutes只有在intelligence模式下才会用到，pro版只支持remixIcon图标
 * hidden:true                        是否显示在菜单中显示路由（默认值：false）
 * alwaysShow:true                    当只有一级子路由时是否显示父路由是否显示在菜单中显示路由（默认值：false）
 * name:"Demo"                        首字母大写，一定要与vue文件的name对应起来，用于noKeepAlive缓存控制（该项特别重要）
 * meta:{
 title:"title"                     菜单、面包屑、多标签页显示的名称
 roles:["admin","..."]             当config/settings.js中rolesControl配置开启时，用于控制角色
 roles: {
 access: ["admin"],                access: ["admin"]
 ability: ["READ","WRITE"],      ability: ["READ","WRITE"],
 mode: "allOf"                   allOf: 数组内所有角色都拥有，返回True
 oneOf: 数组内拥有任一角色，返回True(等价第1种数据)
 except: 不拥有数组内任一角色，返回True(取反)
 }
 remixIcon:""                      remix图标
 isCustomSvgIcon:false,            是否是自定义svg图标（默认值：false，如果设置true，那么需要把你的svg拷贝到icon/remixIcon下，然后remixIcon字段配置上你的图标名）
 noKeepAlive:true                  当前路由是否不缓存（默认值：false）
 affix:true                        当前路由是否固定多标签页
 badge:"New"                       badge小标签（只支持子级）
 tagHidden:true                    当前路由是否不显示多标签页
 }
 */
import * as nodes from "@/access/node";
import { publicPath, routerMode } from "@/config/settings";
import Layout from "@/layouts";
import Vue from "vue";
import VueRouter from "vue-router";

Vue.use(VueRouter);
export const constantRoutes = [
  {
    path: "/ExpireTip",
    component: () => import("@/views/ExpireTip"),
    hidden: true,
  },
  {
    path: "/login",
    component: () => import("@/views/login"),
    hidden: true,
  },
  //收银台登录
  {
    path: "/CashierLogin",
    name: "CashierLogin",
    hidden: true,
    noKeepAlive: true,
    component: () => import("@/views/CashierShop/CashierLogin"),
  },
  // 多商户登录
  {
    path: "/MerchantsLogin",
    name: "MerchantsLogin",
    hidden: true,
    noKeepAlive: true,
    component: () => import("@/views/login/MerchantsLogin"),
  },
  {
    path: "/401",
    name: "401",
    component: () => import("@/views/401"),
    hidden: true,
    noKeepAlive: true,
  },
  {
    path: "/404",
    name: "404",
    component: () => import("@/views/404"),
    hidden: true,
    noKeepAlive: true,
  },
  {
    path: "/AuthorizePage",
    name: "AuthorizePage",
    hidden: true,
    noKeepAlive: true,
    meta: {
      title: "授权",
    },
    component: () => import("@/views/common/AuthorizePage"),
  },
  {
    path: "/MenuList",
    name: "MenuList",
    hidden: true,
    noKeepAlive: true,
    meta: {
      title: "菜单管理",
    },
    component: () => import("@/views/common/MenuList"),
  },
  // 打印
  {
    path: "/OrderPrinting/:userCenterId/:id",
    name: "OrderPrinting",
    hidden: true,
    noKeepAlive: true,
    meta: {
      title: "打印订单",
    },
    component: () => import("@/views/order/manageO/OrderPrinting"),
  },
  {
    path: "/OrderPrintingBetch",
    name: "OrderPrintingBetch",
    hidden: true,
    noKeepAlive: true,
    meta: {
      title: "批量打印订单",
    },
    component: () => import("@/views/order/manageO/OrderPrintingBetch"),
  },
  {
    path: "/OutgoingPrinting/:id",
    name: "OutgoingPrinting",
    hidden: true,
    noKeepAlive: true,
    meta: {
      title: "打印出库单",
    },
    component: () => import("@/views/stock/OutIn/OutgoingPrinting"),
  },
  {
    path: "/StoragePrinting/:id",
    name: "StoragePrinting",
    hidden: true,
    noKeepAlive: true,
    meta: {
      title: "打印入库单",
    },
    component: () => import("@/views/stock/OutIn/StoragePrinting"),
  },
  {
    path: "/ReturnOrderPrinting/:id",
    name: "ReturnOrderPrinting",
    hidden: true,
    noKeepAlive: true,
    meta: {
      title: "打印退货单",
    },
    component: () => import("@/views/order/manageO/ReturnOrderPrinting"),
  },
  {
    path: "/ReconciliationPrint",
    name: "ReconciliationPrint",
    hidden: true,
    noKeepAlive: true,
    meta: {
      title: "打印库存日报",
    },
    component: () => import("@/views/MoneyGoodsBill/order/Reconciliation"),
  },
  {
    path: "/ReconciliationSale",
    name: "ReconciliationSale",
    hidden: true,
    noKeepAlive: true,
    meta: {
      title: "打印销售日报",
    },
    component: () => import("@/views/MoneyGoodsBill/order/ReconciliationSale"),
  },
  {
    path: "/ReconciliationFinance",
    name: "ReconciliationFinance",
    hidden: true,
    noKeepAlive: true,
    meta: {
      title: "打印财务日报",
    },
    component: () => import("@/views/MoneyGoodsBill/order/ReconciliationFinance"),
  },
  {
    path: "/PrintMerchants/:id",
    name: "PrintMerchants",
    hidden: true,
    noKeepAlive: true,
    meta: {
      title: "打印商户入库",
    },
    component: () => import("@/views/stock/OutIn/PrintMerchants"),
  },
  {
    path: "/PrintPurchase/:id",
    name: "PrintPurchase",
    hidden: true,
    noKeepAlive: true,
    meta: {
      title: "打印采购单",
    },
    component: () => import("@/views/Purchase/ManageP/PrintPurchase"),
  },
  {
    path: "/MerchantsProcurement/:id",
    name: "MerchantsProcurement",
    hidden: true,
    noKeepAlive: true,
    meta: {
      title: "打印商户采购单",
    },
    component: () => import("@/views/Purchase/ManageP/MerchantsProcurement"),
  },
  {
    path: "/AllocatingOutbound/:id",
    name: "AllocatingOutbound",
    hidden: true,
    noKeepAlive: true,
    meta: {
      title: "打印调拨出库单",
    },
    component: () => import("@/views/Purchase/ManageP/AllocatingOutbound"),
  },
  // 打印end
  {
    path: "/Enterprise",
    name: "Enterprise",
    noKeepAlive: true,
    component: () => import("@/views/Enterprise/index"),
    hidden: true,
  },
  {
    path: "/CreateStore",
    name: "CreateStore",
    noKeepAlive: true,
    meta: {
      title: "创建企业",
    },
    component: () => import("@/views/Enterprise/CreateStore"),
    hidden: true,
  },
  {
    path: "/EditStore/:id",
    name: "/EditStore",
    noKeepAlive: true,
    meta: {
      title: "编辑企业",
    },
    component: () => import("@/views/Enterprise/CreateStore"),
    hidden: true,
  },
  {
    path: "/SelectStore",
    name: "/SelectStore",
    noKeepAlive: true,
    meta: {
      title: "门店选择",
    },
    component: () => import("@/views/Enterprise/SelectStore"),
    hidden: true,
  },
  {
    path: "/MultiMerchant",
    name: "/MultiMerchant",
    noKeepAlive: true,
    meta: {
      title: "商户选择",
    },
    component: () => import("@/views/Enterprise/MultiMerchant"),
    hidden: true,
  },
];

export const asyncRoutes = [
  {
    path: "/Edit",
    name: "Edit",
    hidden: true,
    component: Layout,
    children: [
      {
        path: "EditPwd/:id",
        name: "EditPwd",
        noKeepAlive: true,
        meta: {
          title: "账号设置",
        },
        component: () => import("@/views/login/EditPwd.vue"),
      },
      {
        path: "EditStore/:id",
        name: "EditStore",
        noKeepAlive: true,
        meta: {
          title: "修改企业",
        },
        component: () => import("@/views/Enterprise/CreateStore"),
        hidden: true,
      },
    ],
  },
  {
    path: "/",
    component: Layout,
    redirect: "/index",
    meta: {
      title: "概况",
      remixIcon: "mac-line",
      affix: true,
    },
    children: [
      {
        path: "index",
        name: "Index",
        component: () => import("@/views/index"),
        noKeepAlive: true,
        meta: {
          title: "经营概况",
          remixIcon: "mac-line",
          affix: true,
        },
      },
      {
        path: "MerchIndex",
        name: "MerchIndex",
        component: () => import("@/viewsMerch/index"),
        noKeepAlive: true,
        meta: {
          title: "经营概况",
          remixIcon: "mac-line",
        },
      },
      {
        path: "test",
        name: "test",
        component: () => import("@/views/index/test"),
        noKeepAlive: true,
        meta: {
          title: "测试vxe-table",
          remixIcon: "mac-line",
        },
      },
      // {
      //   path: "tagPrint",
      //   name: "tagPrint",
      //   component: () => import("@/views/index/tagPrint"),
      //   noKeepAlive: true,
      //   meta: {
      //     title: "标签打印",
      //     remixIcon: "mac-line",
      //   },
      // },
    ],
  },
  {
    path: "/goods",
    component: Layout,
    redirect: "/goods/manageG/BaseDataList",
    alwaysShow: true,
    meta: {
      title: "商品",
      remixIcon: "shopping-bag-line",
    },
    children: [
      {
        path: "manageG/GoodsImport",
        name: "GoodsImport",
        component: () => import("@/views/goods/manageG/GoodsImport.vue"),
        meta: {
          title: "导入商品",
          remixIcon: "apps-line",
          // access: nodes.BaseDataList,
        },
      },
      {
        path: "manageG/SingleGoodsImport",
        name: "SingleGoodsImport",
        component: () => import("@/views/goods/manageG/SingleGoodsImport.vue"),
        meta: {
          title: "单店铺导入商品",
          remixIcon: "apps-line",
          // access: nodes.BaseDataList,
        },
      },
      {
        path: "manageG/BaseDataList",
        name: "BaseDataList",
        component: () => import("@/views/goods/manageG/BaseDataList.vue"),
        meta: {
          title: "商品资料",
          remixIcon: "apps-line",
          access: nodes.BaseDataList,
        },
      },
      {
        path: "manageG/EditBaseData/:id",
        name: "EditBaseData",
        hidden: true,
        meta: {
          title: "编辑商品资料",
          remixIcon: "apps-line",
          access: nodes.EditBaseData,
        },
        component: () => import("@/views/goods/manageG/AddBaseData"),
      },
      {
        path: "manageG/AddBaseData",
        name: "AddBaseData",
        hidden: true,
        meta: {
          title: "新建商品资料",
          remixIcon: "apps-line",
          access: nodes.AddBaseData,
        },
        component: () => import("@/views/goods/manageG/AddBaseData"),
      },
      {
        path: "manageG/BaseDataDetail/:id",
        name: "BaseDataDetail",
        hidden: true,
        meta: {
          title: "商品详情",
          remixIcon: "apps-line",
          access: nodes.BaseDataListDetail,
        },
        component: () => import("@/views/goods/manageG/AddBaseData"),
      },
      {
        path: "manageG/GoodsClassify",
        name: "GoodsClassify",
        meta: {
          title: "商品分类",
          remixIcon: "apps-line",
          access: nodes.GoodsClassify,
        },
        component: () => import("@/views/goods/manageG/GoodsClassify"),
      },
      {
        path: "manageG/GoodsGrouping",
        name: "GoodsGrouping",
        meta: {
          title: "商品分组",
          remixIcon: "apps-line",
          access: nodes.GoodsGrouping,
        },
        component: () => import("@/views/goods/manageG/GoodsGrouping"),
      },
      {
        path: "manageG/BrandManage",
        name: "BrandManage",
        meta: {
          title: "商品品牌",
          remixIcon: "apps-line",
          access: nodes.BrandManage,
        },
        component: () => import("@/views/goods/manageG/BrandManage"),
      },
      /* {
       path: 'manageG/AddBrand',
       name: 'AddBrand',
       meta: {
       title: '新增品牌',
       },
       component: () => import('@/views/goods/manageG/AddBrand')
       },
       {
       path: 'manageG/UpdateBrand/:id',
       name: 'UpdateBrand',
       meta: {
       title: '编辑品牌',
       },
       component: () => import('@/views/goods/manageG/AddBrand')
       }, */
      {
        path: "manageG/UnitMeasurement",
        name: "UnitMeasurement",
        meta: {
          title: "单位管理",
          remixIcon: "apps-line",
          access: nodes.UnitSet,
        },
        component: () => import("@/views/goods/manageG/UnitMeasurement"),
      },
      {
        path: "manageG/SpecManage",
        name: "SpecManage",
        meta: {
          title: "属性管理",
          remixIcon: "apps-line",
          access: nodes.specManage,
        },
        component: () => import("@/views/goods/manageG/SpecManage"),
      },

      {
        path: "sale/PublishGoods",
        name: "PublishGoods",
        component: () => import("@/views/goods/sale/PublishGoods.vue"),
        meta: {
          title: "商品列表",
          access: nodes.PublishGoods,
          remixIcon: "apps-line",
        },
      },
      //多商户商品列表
      {
        path: "sale/MerchantsGoods",
        name: "MerchantsGoods",
        component: () => import("@/viewsMerch/goods/GoodsList.vue"),
        meta: {
          title: "商品列表",
          remixIcon: "apps-line",
        },
      },
      {
        path: "sale/AddGoods",
        name: "AddGoods",
        hidden: true,
        meta: {
          title: "发布商品",
          access: nodes.PublishGoodsAddGoods,
          remixIcon: "apps-line",
        },
        component: () => import("@/views/goods/sale/AddGoods"),
      },
      {
        path: "sale/EditGoods/:id",
        name: "EditGoods",
        hidden: true,
        meta: {
          title: "修改商品",
          access: nodes.PublishGoodsEditGoods,
          remixIcon: "apps-line",
        },
        component: () => import("@/views/goods/sale/AddGoods"),
      },
      {
        path: "sale/AddGoodsOneStore",
        name: "AddGoodsOneStore",
        hidden: true,
        meta: {
          title: "新建商品",
          remixIcon: "apps-line",
          access: nodes.PublishGoodsAddBasicAndPublishGoods,
        },
        component: () => import("@/views/goods/sale/AddGoodsOneStore"),
      },
      {
        path: "sale/EditGoodsOneStore/:id",
        name: "EditGoodsOneStore",
        hidden: true,
        meta: {
          title: "修改商品",
          remixIcon: "apps-line",
          access: nodes.PublishGoodsEditQuickGoods,
        },
        component: () => import("@/views/goods/sale/AddGoodsOneStore"),
      },
      {
        path: "sale/GoodsDetail/:id",
        name: "GoodsDetail",
        hidden: true,
        meta: {
          title: "商品详请",
          remixIcon: "apps-line",
          access: nodes.PublishGoodsGetGoodsInfo,
        },
        component: () => import("@/views/goods/sale/GoodsDetail"),
      },
      {
        path: "sale/UpdateGoods/:id",
        name: "UpdateGoods",
        hidden: true,
        meta: {
          title: "编辑商品",
          remixIcon: "apps-line",
          access: nodes.PublishGoodsEditGoods,
        },
        component: () => import("@/views/goods/sale/AddGoods"),
      },
      // 商品服务
      {
        path: "goodsemprego/GoodsEmprego",
        name: "GoodsEmprego",
        hidden: true,
        meta: {
          title: "商品服务",
          remixIcon: "apps-line",
          access: nodes.GoodsEmprego,
        },
        component: () => import("@/views/goods/goodsemprego/GoodsEmprego"),
      },
      {
        path: "sale/priceTable",
        name: "priceTable",
        meta: {
          title: "价格管理",
          remixIcon: "bookmark-3-line",
        },
        component: () => import("@/views/goods/sale/PriceTable"),
      },
      {
        path: "sale/CostumeAdjustPrice",
        name: "CostumeAdjustPrice",
        meta: {
          title: "客户调价单",
          remixIcon: "bookmark-3-line",
          access: nodes.CostumeAdjustPrice,
        },
        component: () => import("@/views/goods/sale/CostumeAdjustPrice"),
      },
      {
        path: "sale/CostumeAdjustPriceAdd",
        name: "CostumeAdjustPriceAdd",
        hidden: true,
        meta: {
          title: "新建客户调价单",
          remixIcon: "bookmark-3-line",
          access: nodes.CostumeAdjustPriceAdd,
        },
        component: () => import("@/views/goods/sale/CostumeAdjustPriceAdd"),
      },
      {
        path: "sale/CustomerTypeAdjustPrice",
        name: "CustomerTypeAdjustPrice",
        meta: {
          title: "客户类型调价单",
          remixIcon: "bookmark-3-line",
          access: nodes.CustomerTypeAdj,
        },
        component: () => import("@/views/goods/sale/CustomerTypeAdjustPrice"),
      },
      {
        path: "sale/CostumeTypeAdjustPriceAdd",
        name: "CostumeTypeAdjustPriceAdd",
        hidden: true,
        meta: {
          title: "新建客户类型调价单",
          remixIcon: "bookmark-3-line",
          access: nodes.CustomerTypeAdjAdd,
        },
        component: () => import("@/views/goods/sale/CostumeTypeAdjustPriceAdd"),
      },
      {
        path: "sale/AdjustPrice",
        name: "AdjustPrice",
        meta: {
          title: "商品调价单",
          remixIcon: "bookmark-3-line",
          access: nodes.AdjustPrice,
        },
        component: () => import("@/views/goods/sale/AdjustPrice"),
      },
      {
        path: "sale/AdjustPriceGoods",
        name: "AdjustPriceGoods",
        meta: {
          title: "调价单",
          remixIcon: "bookmark-3-line",
        },
        component: () => import("@/views/goods/sale/AdjustPriceGoods"),
      },
      {
        path: "sale/AddGoodsAdjustPrice",
        name: "AddGoodsAdjustPrice",
        hidden: true,
        meta: {
          title: "新建商品调价单",
          remixIcon: "bookmark-3-line",
          access: nodes.AdjustPriceAdd,
        },
        component: () => import("@/views/goods/sale/AddGoodsAdjustPrice"),
      },
    ],
  },
  {
    path: "/order",
    name: "Order",
    component: Layout,
    redirect: "/order/manageO/orderQuery",
    alwaysShow: true,
    meta: {
      title: "订单",
      remixIcon: "file-list-2-line",
    },
    children: [
      /* {
       path: "manageO/newOrderList",
       name: "NewOrderList",
       meta: {
       title: "新订单列表",
       access: nodes.newOrderList,
       remixIcon: "list-unordered",
       },
       component: () => import("@/views/order/manageO/NewOrderList"),
       },*/
      {
        path: "manageO/orderQuery",
        name: "OrderQuery",
        meta: {
          title: "订货单",
          access: nodes.orderQuery,
          remixIcon: "list-unordered",
        },
        component: () => import("@/views/order/manageO/NewOrderList"),
      },
      {
        path: "manageO/OrderDetails/:userCenterId/:id",
        name: "OrderDetails",
        meta: {
          title: "订单详情",
          // access: nodes.OrderDetails,
          remixIcon: "list-unordered",
        },
        component: () => import("@/views/order/manageO/OrderDetails"),
      },
      {
        path: "manageO/selfOrder",
        name: "SelfOrder",
        meta: {
          title: "自提单",
          access: nodes.selfOrder,
          remixIcon: "list-unordered",
        },
        component: () => import("@/views/order/manageO/NewOrderList"),
      },
      /*{
       path: "manageO/CancelOrder",
       name: "CancelOrder",
       meta: {
       title: "已取消",
       access: nodes.CancelOrder,
       remixIcon: "list-unordered",
       },
       component: () => import("@/views/order/manageO/CancelOrder"),
       },*/
      {
        path: "manageO/OrderAdd",
        name: "OrderAdd",
        meta: {
          title: "代客下单",
          access: nodes.OrderAdd,
          remixIcon: "list-unordered",
        },
        component: () => import("@/views/order/manageO/OrderAdd"),
      },
      {
        path: "manageO/OrderEdit/:userCenterId/:id",
        name: "OrderEdit",
        hidden: true,
        meta: {
          title: "编辑订单",
          access: nodes.newOrderListEdit,
          remixIcon: "list-unordered",
        },
        component: () => import("@/views/order/manageO/OrderAdd"),
      },
      {
        path: "manageO/ReturnWarehousingOrder",
        name: "ReturnWarehousingOrder",
        meta: {
          title: "退货单",
          access: nodes.ReturnWarehousingOrder,
          remixIcon: "list-unordered",
        },
        component: () => import("@/views/order/manageO/ReturnWarehousingOrder"),
      },
      {
        path: "manageO/AddWareOrder",
        name: "AddWareOrder",
        hidden: true,
        meta: {
          title: "新增退货单",
          access: nodes.ReturnWarehousingOrderAddOrderReturn,
        },
        component: () => import("@/views/order/manageO/AddWareOrder"),
      },
      {
        path: "manageO/EditWareOrder/:id",
        name: "EditWareOrder",
        hidden: true,
        meta: {
          title: "编辑退货单",
          access: nodes.ReturnWarehousingOrderUpdateOrderReturn,
          remixIcon: "list-unordered",
        },
        component: () => import("@/views/order/manageO/AddWareOrder"),
      },
      {
        path: "manageO/LookWareOrder/:id",
        name: "LookWareOrder",
        hidden: true,
        meta: {
          title: "查看退货单",
          access: nodes.ReturnWarehousingOrderGetOrderReturn,
          remixIcon: "list-unordered",
        },
        component: () => import("@/views/order/manageO/LookWareOrder"),
      },
      /* {
       path: "saleO/AddSaleOrder",
       name: "AddSaleOrder",
       meta: {
       title: "创建销售单",
       access: nodes.addSaleOrder,
       remixIcon: "bill-line",
       },
       component: () => import("@/views/order/saleO/AddSaleOrder"),
       },
       {
       path: "saleO/SaleOrderList",
       name: "SaleOrderList",
       meta: {
       title: "销售单列表",
       access: nodes.saleOrderList,
       remixIcon: "bill-line",
       },
       component: () => import("@/views/order/saleO/SaleOrderList"),
       },*/
      {
        path: "SaleTotalForm/GoodsForm",
        name: "GoodsForm",
        meta: {
          title: "商品汇总表",
          access: nodes.OrderStatistics,
          remixIcon: "file-text-line",
        },
        component: () => import("@/views/order/SaleTotalForm/TotalForm"),
      },
      {
        path: "SaleTotalForm/CustomerForm",
        name: "CustomerForm",
        meta: {
          title: "客户汇总表",
          access: nodes.OrderStatistics,
          remixIcon: "file-text-line",
        },
        component: () => import("@/views/order/SaleTotalForm/TotalForm"),
      },
      {
        path: "SaleTotalForm/StaffForm",
        name: "StaffForm",
        meta: {
          title: "人员汇总表",
          access: nodes.OrderStatistics,
          remixIcon: "file-text-line",
        },
        component: () => import("@/views/order/SaleTotalForm/TotalForm"),
      },
      {
        path: "SaleTotalForm/SaleSummary",
        name: "SaleSummary",
        hidden: true,
        meta: {
          title: "销售毛利明细表(不含税价)",
          remixIcon: "file-text-line",
        },
        component: () => import("@/views/order/SaleTotalForm/SaleSummary"),
      },
    ],
  },
  {
    path: "/Customer",
    name: "Customer",
    component: Layout,
    redirect: "/Customer/CustomerAdmin/CustomerList",
    alwaysShow: true,
    meta: {
      title: "客户",
      remixIcon: "contacts-line",
    },
    children: [
      {
        path: "CustomerAdmin/CustomerImport",
        name: "CustomerImport",
        meta: {
          title: "导入客户",
          remixIcon: "bookmark-3-line",
          // access: nodes.CostumeAdjustPrice,
        },
        component: () => import("@/views/Customer/CustomerAdmin/CustomerImport.vue"),
      },
      {
        path: "CustomerAdmin/CustomerList",
        name: "CustomerList",
        meta: {
          title: "客户列表",
          access: nodes.CustomerList,
          remixIcon: "user-3-line",
        },
        component: () => import("@/views/Customer/CustomerAdmin/CustomerList.vue"),
      },
      {
        path: "CustomerAdmin/SaldoDetail",
        name: "SaldoDetail",
        meta: {
          title: "余额明细",
          remixIcon: "user-3-line",
          access: nodes.CustomerListSaldoDetail,
        },
        component: () => import("@/views/Customer/CustomerAdmin/SaldoDetail.vue"),
      },
      // 积分明细
      {
        path: "CustomerAdmin/IntegralDetail/:id",
        name: "IntegralDetail",
        hidden: true,
        meta: {
          title: "积分明细",
          remixIcon: "user-3-line",
          access: nodes.CustomerListIntegralDetail,
        },
        component: () => import("@/views/Customer/CustomerAdmin/IntegralDetail.vue"),
      },
      {
        path: "CustomerAdmin/AddCustomer",
        name: "AddCustomer",
        hidden: true,
        meta: {
          title: "新增客户",
          access: nodes.CustomerListAddCustomer,
          remixIcon: "user-3-line",
        },
        component: () => import("@/views/Customer/CustomerAdmin/AddCustomer.vue"),
      },
      {
        path: "CustomerAdmin/EditCustomer/:id",
        name: "EditCustomer",
        hidden: true,
        meta: {
          title: "编辑客户",
          access: nodes.CustomerListEditCustomer,
          remixIcon: "user-3-line",
        },
        component: () => import("@/views/Customer/CustomerAdmin/AddCustomer.vue"),
      },
      {
        path: "CustomerAdmin/CustomerDetail/:id",
        name: "CustomerDetail",
        hidden: true,
        meta: {
          title: "客户详情",
          access: nodes.CustomerListGetCustomerInfo,
          remixIcon: "user-3-line",
        },
        component: () => import("@/views/Customer/CustomerAdmin/CustomerDetail.vue"),
      },
      {
        path: "CustomerAdmin/CustomerType",
        name: "CustomerType",
        meta: {
          title: "客户类型",
          access: nodes.CustomerType,
          remixIcon: "user-3-line",
        },
        component: () => import("@/views/Customer/CustomerAdmin/CustomerType.vue"),
      },
      {
        path: "CustomerAdmin/LabelManagement",
        name: "LabelManagement",
        meta: {
          title: "标签管理",
          remixIcon: "user-3-line",
          access: nodes.LabelManagement,
        },
        component: () => import("@/views/Customer/CustomerAdmin/LabelManagement.vue"),
      },
      {
        path: "CustomerAdmin/CustomerQuery",
        name: "CustomerQuery",
        meta: {
          title: "客户查询",
          access: nodes.CustomerQuery,
          remixIcon: "user-3-line",
        },
        component: () => import("@/views/Customer/CustomerAdmin/CustomerQuery.vue"),
      },
      {
        path: "CustomerAdmin/CustomerClustering",
        name: "CustomerClustering",
        hidden: true,
        meta: {
          title: "客户分群",
          access: nodes.CustomerClustering,
          remixIcon: "user-3-line",
        },
        component: () => import("@/views/Customer/CustomerAdmin/CustomerClustering.vue"),
      },
      {
        path: "CustomerAdmin/AddClustering",
        name: "AddClustering",
        hidden: true,
        meta: {
          title: "新建人群",
          remixIcon: "user-3-line",
        },
        component: () => import("@/views/Customer/CustomerAdmin/AddClustering.vue"),
      },
      {
        path: "CustomerAdmin/EditClustering/:id",
        name: "EditClustering",
        hidden: true,
        meta: {
          title: "编辑人群",
          remixIcon: "user-3-line",
        },
        component: () => import("@/views/Customer/CustomerAdmin/AddClustering.vue"),
      },
      {
        path: "CustomerAdmin/ClusteringAnalyze/:id",
        name: "ClusteringAnalyze",
        hidden: true,
        meta: {
          title: "人群分析",
          remixIcon: "user-3-line",
        },
        component: () => import("@/views/Customer/CustomerAdmin/ClusteringAnalyze.vue"),
      },

      {
        path: "CustomerCheck/NotCheck",
        name: "NotCheck",
        meta: {
          title: "未审核",
          access: nodes.NotCheck,
          remixIcon: "user-star-line",
        },
        component: () => import("@/views/Customer/CustomerCheck/CustomerCheck.vue"),
      },
      {
        path: "CustomerCheck/noPerfectData",
        name: "NoPerfectData",
        meta: {
          title: "待完善资料",
          access: nodes.getAuditAllCustomer,
          remixIcon: "user-star-line",
        },
        component: () => import("@/views/Customer/CustomerCheck/CustomerCheck.vue"),
      },
      {
        path: "CustomerBehavior/BrowsingHistory",
        name: "BrowsingHistory",
        meta: {
          title: "浏览记录",
          remixIcon: "user-star-line",
          access: nodes.CustomerBehaviorBrowsingHistory,
        },
        component: () => import("@/views/Customer/CustomerBehavior/BrowsingHistory.vue"),
      },
      {
        path: "CustomerBehavior/PurchaseHistory",
        name: "PurchaseHistory",
        meta: {
          title: "购买记录",
          remixIcon: "user-star-line",
          access: nodes.CustomerBehaviorPurchaseHistory,
        },
        component: () => import("@/views/Customer/CustomerBehavior/PurchaseHistory.vue"),
      },
      {
        path: "CustomerBehavior/DemandReporting",
        name: "DemandReporting",
        meta: {
          title: "需求提报",
          remixIcon: "user-star-line",
          access: nodes.CustomerBehaviorDemandReporting,
        },
        component: () => import("@/views/Customer/CustomerBehavior/DemandReporting.vue"),
      },
      {
        path: "CustomerStatements/AnomalyAnalysisTable",
        name: "AnomalyAnalysisTable",
        meta: {
          title: "异常客户分析表",
          access: nodes.CustomerStatementsAnomalyAnalysisTable,
          remixIcon: "user-star-line",
        },
        component: () => import("@/views/Customer/CustomerStatements/AnomalyAnalysisTable.vue"),
      },
      {
        path: "CustomerStatements/PullNewStatistics",
        name: "PullNewStatistics",
        meta: {
          title: "拉新统计",
          remixIcon: "user-star-line",
          access: nodes.CustomerStatementsPullNewStatistics,
        },
        component: () => import("@/views/Customer/CustomerStatements/PullNewStatistics.vue"),
      },
      {
        path: "CustomerStatements/VisitRepor",
        name: "VisitRepor",
        meta: {
          title: "拜访报表",
          remixIcon: "user-star-line",
          access: nodes.CustomerStatementsVisitRepor,
        },
        component: () => import("@/views/Customer/CustomerStatements/VisitRepor.vue"),
      },
      {
        path: "CustomerStatements/CustomerMap",
        name: "CustomerMap",
        meta: {
          title: "客户分布图",
          remixIcon: "user-star-line",
          access: nodes.CustomerStatementsCustomerDistribution,
        },
        component: () => import("@/views/Customer/CustomerStatements/CustomerMap.vue"),
      },
      // {
      //   path: "CustomerStatements/StaffPullNewStatistics",
      //   name: "StaffPullNewStatistics",
      //   meta: {
      //     title: "员工拉新统计",
      //     remixIcon: "user-star-line",
      //   },
      //   component: () =>
      //     import(
      //       "@/views/Customer/CustomerStatements/StaffPullNewStatistics.vue"
      //     ),
      // },
    ],
  },
  {
    path: "/Purchase",
    component: Layout,
    redirect: "/Purchase/ManageP/PurchaseOrder",
    alwaysShow: true,
    meta: {
      title: "采购",
      remixIcon: "shopping-cart-line",
    },
    children: [
      {
        path: "ManageP/Supplier",
        name: "Supplier",
        meta: {
          title: "供应商管理",
          access: nodes.Supplier,
          remixIcon: "shopping-cart-line",
        },
        component: () => import("@/views/Purchase/ManageP/Supplier"),
      },
      {
        path: "ManageP/Stockout",
        name: "Stockout",
        meta: {
          title: "缺货单",
          access: nodes.Stockout,
          remixIcon: "shopping-cart-line",
        },
        component: () => import("@/views/Purchase/ManageP/Stockout"),
      },
      {
        path: "ManageP/SupplierAdd",
        name: "SupplierAdd",
        hidden: true,
        meta: {
          title: "新增供应商",
          access: nodes.SupplierAddSupplier,
          remixIcon: "shopping-cart-line",
        },
        component: () => import("@/views/Purchase/ManageP/SupplierAdd"),
      },
      {
        path: "ManageP/SupplierImport",
        name: "SupplierImport",
        hidden: true,
        meta: {
          title: "导入供应商",
          // access: nodes.SupplierAddSupplier,
          remixIcon: "shopping-cart-line",
        },
        component: () => import("@/views/Purchase/ManageP/SupplierImport"),
      },
      {
        path: "ManageP/NewMerchant",
        name: "NewMerchant",
        hidden: true,
        meta: {
          title: "新增商户",
          // access: nodes.SupplierAddSupplier,
          remixIcon: "shopping-cart-line",
        },
        component: () => import("@/views/Purchase/ManageP/NewMerchant"),
      },
      {
        path: "ManageP/SupplierEdit/:id",
        name: "SupplierEdit",
        hidden: true,
        meta: {
          title: "编辑供应商",
          access: nodes.SupplierEditSupplier,
          remixIcon: "shopping-cart-line",
        },
        component: () => import("@/views/Purchase/ManageP/SupplierAdd"),
      },
      {
        path: "ManageP/SupplierLook/:id",
        name: "SupplierLook",
        hidden: true,
        meta: {
          title: "查看供应商",
          access: nodes.SupplierGetSupplierInfoById,
          remixIcon: "shopping-cart-line",
        },
        component: () => import("@/views/Purchase/ManageP/SupplierAdd"),
      },
      {
        path: "ManageP/PurchaseOrder",
        name: "PurchaseOrder",
        meta: {
          title: "采购单",
          access: nodes.PurchaseOrder,
          remixIcon: "shopping-cart-line",
        },
        component: () => import("@/views/Purchase/ManageP/PurchaseOrder"),
      },
      {
        path: "ManageP/PurchaseOrderAdd/",
        name: "PurchaseOrderAdd",
        noKeepAlive: true,
        hidden: true,
        meta: {
          title: "新增采购单",
          access: nodes.PurchaseOrderAddPurchase,
          remixIcon: "shopping-cart-line",
        },
        component: () => import("@/views/Purchase/ManageP/PurchaseOrderAdd"),
      },
      {
        path: "ManageP/PurchaseOrderEdit/:id",
        name: "PurchaseOrderEdit",
        hidden: true,
        meta: {
          title: "编辑采购单",
          access: nodes.PurchaseOrderEditPurchase,
          remixIcon: "shopping-cart-line",
        },
        component: () => import("@/views/Purchase/ManageP/PurchaseOrderAdd"),
      },
      {
        path: "ManageP/PurchaseOrderLook/:id",
        name: "PurchaseOrderLook",
        hidden: true,
        meta: {
          title: "查看采购单",
          access: nodes.PurchaseOrderAddPurchase,
          remixIcon: "shopping-cart-line",
        },
        component: () => import("@/views/Purchase/ManageP/PurchaseOrderLook"),
      },
      {
        path: "ManageP/PurchaseReturnOrder",
        name: "PurchaseReturnOrder",
        meta: {
          title: "采购退货单",
          access: nodes.PurchaseReturnOrder,
          remixIcon: "shopping-cart-line",
        },
        component: () => import("@/views/Purchase/ManageP/PurchaseReturnOrder"),
      },
      {
        path: "ManageP/Merchant",
        name: "Merchant",
        meta: {
          title: "商户采购单",
          access: nodes.Merchant,
          remixIcon: "shopping-cart-line",
        },
        component: () => import("@/views/Purchase/ManageP/Merchant"),
      },
      {
        path: "ManageP/AddMerchantPurchase",
        name: "AddMerchant",
        meta: {
          title: "新增商户采购单",
          access: nodes.MerchantAddMerchant,
          remixIcon: "shopping-cart-line",
        },
        component: () => import("@/views/Purchase/ManageP/AddMerchantPurchase"),
      },
      {
        path: "ManageP/EditMerchantPurchase/:id",
        name: "EditMerchantPurchase",
        meta: {
          title: "编辑商户采购单",
          access: nodes.MerchantPurchaseOrderEdit,
          remixIcon: "shopping-cart-line",
        },
        component: () => import("@/views/Purchase/ManageP/AddMerchantPurchase"),
      },
      {
        path: "ManageP/MerchantPurchaseDetail/:id",
        name: "MerchantPurchaseDetail",
        meta: {
          title: "商户采购单详情",
          access: nodes.MerchantPurchaseOrderEdit,
          remixIcon: "shopping-cart-line",
        },
        component: () => import("@/views/Purchase/ManageP/MerchantPurchaseDetail"),
      },
      {
        path: "ManageP/PurchaseReturnOrderAdd/:type",
        name: "PurchaseReturnOrderAdd",
        hidden: true,
        meta: {
          title: "新增采购退货单",
          access: nodes.PurchaseReturnOrderAddPurchaseOut,
          remixIcon: "shopping-cart-line",
        },
        component: () => import("@/views/Purchase/ManageP/PurchaseReturnOrderAdd"),
      },
      {
        path: "ManageP/PurchaseReturnOrderEdit/:id/:type",
        name: "PurchaseReturnOrderEdit",
        hidden: true,
        meta: {
          title: "编辑采购退货单",
          access: nodes.PurchaseReturnOrderEditPurchase,
          remixIcon: "shopping-cart-line",
        },
        component: () => import("@/views/Purchase/ManageP/PurchaseReturnOrderAdd"),
      },
      {
        path: "ManageP/PurchaseReturnOrderLook/:id",
        name: "PurchaseReturnOrderLook",
        hidden: true,
        meta: {
          title: "查看采购退货单",
          access: nodes.PurchaseReturnOrderGetPurchaseOutInfoById,
          remixIcon: "shopping-cart-line",
        },
        component: () => import("@/views/Purchase/ManageP/PurchaseReturnOrderLook"),
      },
      {
        path: "ManageP/PurchaseDetail",
        name: "PurchaseDetail",
        meta: {
          title: "采购明细",
          access: nodes.PurchaseDetail,
          remixIcon: "shopping-cart-line",
        },
        component: () => import("@/views/Purchase/ManageP/PurchaseDetail"),
      },

      {
        path: "PurchaseTotalForm/GoodsForm",
        name: "PurchaseGoodsForm",
        meta: {
          title: "采购商品汇总表",
          access: nodes.PurchaseStatisticsGetAllPurchaseByFields,
          remixIcon: "file-text-line",
        },
        component: () => import("@/views/Purchase/PurchaseTotalForm/TotalForm"),
      },
      {
        path: "PurchaseTotalForm/SupplierForm",
        name: "PurchaseSupplierForm",
        meta: {
          title: "采购供应商汇总表",
          remixIcon: "file-text-line",
          access: nodes.GetSupplierAllPurchaseByFields,
        },
        component: () => import("@/views/Purchase/PurchaseTotalForm/TotalForm"),
      },
      {
        path: "PurchaseTotalForm/StaffForm",
        name: "PurchaseStaffForm",
        meta: {
          title: "采购人员汇总表",
          access: nodes.GetStaffAllPurchaseByFields,
          remixIcon: "file-text-line",
        },
        component: () => import("@/views/Purchase/PurchaseTotalForm/TotalForm"),
      },
      {
        path: "CostPrice/CostPriceList",
        name: "CostPriceList",
        meta: {
          title: "成本价",
          access: nodes.CostPriceGetAllCostPrice,
          remixIcon: "file-text-line",
        },
        component: () => import("@/views/Purchase/CostPrice/CostPriceList"),
      },
      {
        path: "CostPrice/CostPriceAdd",
        name: "CostPriceAdd",
        meta: {
          title: "新增成本价",
          access: nodes.CostPriceGetAllCostPrice,
          remixIcon: "file-text-line",
        },
        component: () => import("@/views/Purchase/CostPrice/CostPriceForm"),
      },
      {
        path: "CostPrice/CostPriceEdit/:id",
        name: "CostPriceEdit",
        meta: {
          title: "编辑成本价",
          access: nodes.CostPriceEditCostPrice,
          remixIcon: "file-text-line",
        },
        component: () => import("@/views/Purchase/CostPrice/CostPriceForm"),
      },
    ],
  },
  {
    path: "/stock",
    component: Layout,
    redirect: "/stock/OutIn/outgoing",
    alwaysShow: true,
    meta: {
      title: "库存",
      remixIcon: "home-8-line",
    },
    children: [
      {
        path: "OutIn/outgoing",
        name: "Outgoing",
        meta: {
          title: "出库管理",
          access: nodes.InventoryOut,
          remixIcon: "home-gear-line",
        },
        component: () => import("@/views/stock/OutIn/outgoing"),
      },
      {
        path: "OutIn/outgoingInfo/:id",
        name: "OutgoingInfo",
        hidden: true,
        meta: {
          title: "出库单详情",
          access: nodes.InventoryOutGetInventoryOutInfo,
          remixIcon: "home-gear-line",
        },
        component: () => import("@/views/stock/OutIn/outgoingInfo"),
      },
      {
        path: "OutIn/storage",
        name: "Storage",
        meta: {
          title: "入库管理",
          access: nodes.InventoryIn,
          remixIcon: "home-gear-line",
        },
        component: () => import("@/views/stock/OutIn/storage"),
      },
      {
        path: "OutIn/AddStorage",
        name: "AddStorage",
        meta: {
          title: "新增入库单",
          // access: nodes.InventoryIn,
          remixIcon: "home-gear-line",
        },
        component: () => import("@/views/stock/OutIn/AddStorage"),
      },
      {
        path: "OutIn/storageInfo/:id",
        name: "StorageInfo",
        hidden: true,
        meta: {
          title: "入库单详情",
          access: nodes.InventoryInGetInventoryInInfo,
          remixIcon: "home-gear-line",
        },
        component: () => import("@/views/stock/OutIn/storageInfo"),
      },
      {
        path: "WarehouseAdmin/query",
        name: "Query",
        meta: {
          title: "库存查询",
          access: nodes.query,
          remixIcon: "home-gear-line",
        },
        component: () => import("@/views/stock/WarehouseAdmin/query"),
      },
      {
        path: "WarehouseAdmin/warehouseInventory",
        name: "warehouseInventory",
        meta: {
          title: "仓库库存",
          access: nodes.warehouseInventory,
          remixIcon: "home-gear-line",
        },
        component: () => import("@/views/stock/WarehouseAdmin/warehouseInventory"),
      },
      {
        path: "WarehouseAdmin/flowing",
        name: "Flowing",
        meta: {
          title: "库存流水",
          access: nodes.flowing,
          remixIcon: "home-gear-line",
        },
        component: () => import("@/views/stock/WarehouseAdmin/flowing"),
      },
      {
        path: "WarehouseAdmin/Batch",
        name: "Batch",
        meta: {
          title: "批次流水",
          access: nodes.Batch,
          remixIcon: "home-gear-line",
        },
        component: () => import("@/views/stock/WarehouseAdmin/Batch"),
      },
      {
        path: "WarehouseAdmin/ShelfLife",
        name: "ShelfLife",
        meta: {
          title: "保质期查询",
          access: nodes.GetBatch,
          remixIcon: "home-gear-line",
        },
        component: () => import("@/views/stock/WarehouseAdmin/ShelfLife"),
      },
      {
        path: "WarehouseManagement/Management",
        name: "Management",
        meta: {
          title: "仓库管理",
          access: nodes.management,
          remixIcon: "home-6-line",
        },
        component: () => import("@/views/stock/WarehouseManagement/Management"),
      },
      {
        path: "WarehouseManagement/InitStockImport",
        name: "InitStockImport",
        hidden: true,
        meta: {
          title: "初始化导入",
          // access: nodes.SupplierAddSupplier,
          remixIcon: "shopping-cart-line",
        },
        component: () => import("@/views/stock/WarehouseManagement/InitStockImport"),
      },
      {
        path: "WarehouseManagement/WarehouseArea",
        name: "WarehouseArea",
        meta: {
          title: "库区管理",
          access: nodes.WarehouseArea,
          remixIcon: "home-6-line",
        },
        component: () => import("@/views/stock/WarehouseManagement/WarehouseArea"),
      },
      {
        path: "WarehouseManagement/LocationManagement",
        name: "LocationManagement",
        meta: {
          title: "库位管理",
          access: nodes.LocationManagement,
          remixIcon: "home-6-line",
        },
        component: () => import("@/views/stock/WarehouseManagement/LocationManagement"),
      },
      {
        path: "WarehouseManagement/newWarehouse",
        name: "NewWarehouse",
        hidden: true,
        meta: {
          title: "新建仓库",
          access: nodes.managementAddWarehouse,
          remixIcon: "home-6-line",
        },
        component: () => import("@/views/stock/WarehouseManagement/newWarehouse"),
      },
      {
        path: "WarehouseManagement/eitWarehouse/:id",
        name: "EitWarehouse",
        hidden: true,
        meta: {
          title: "编辑仓库",
          access: nodes.managementUpdateWarehouse,
        },
        component: () => import("@/views/stock/WarehouseManagement/newWarehouse"),
      },
      {
        path: "WarehouseManagement/start",
        name: "Start",
        hidden: true,
        meta: {
          title: "期初库存",
          remixIcon: "home-6-line",
        },
        component: () => import("@/views/stock/WarehouseManagement/start"),
      },
      {
        path: "WarehouseManagement/startInfo",
        name: "StartInfo",
        hidden: true,
        meta: {
          title: "期初库存单详情",
          remixIcon: "home-6-line",
        },
        component: () => import("@/views/stock/WarehouseManagement/startInfo"),
      },
      {
        path: "WarehouseManagement/newStart",
        name: "NewStart",
        hidden: true,
        meta: {
          title: "新建期初库存",
          access: nodes.managementAddWarehouseBeginning,
          remixIcon: "home-6-line",
        },
        component: () => import("@/views/stock/WarehouseManagement/newStart"),
      },
      {
        path: "WarehouseManagement/inventoryInfo",
        name: "InventoryInfo",
        meta: {
          title: "盘点单",
          access: nodes.inventoryInfo,
          remixIcon: "home-6-line",
        },
        component: () => import("@/views/stock/WarehouseManagement/inventoryInfo"),
      },
      {
        path: "WarehouseManagement/AddInventoryInfo",
        name: "AddInventoryInfo",
        hidden: true,
        meta: {
          title: "新建盘点单",
          access: nodes.inventoryInfoAddStocktaking,
          remixIcon: "home-6-line",
        },
        component: () => import("@/views/stock/WarehouseManagement/AddInventoryInfo.vue"),
      },
      {
        path: "WarehouseManagement/EditInventoryInfo/:id",
        name: "EditInventoryInfo",
        hidden: true,
        meta: {
          title: "编辑盘点单",
          access: nodes.inventoryInfoUpdateStocktaking,
          remixIcon: "home-6-line",
        },
        component: () => import("@/views/stock/WarehouseManagement/AddInventoryInfo.vue"),
      },
      {
        path: "WarehouseManagement/InventoryDetail/:id",
        name: "InventoryDetail",
        hidden: true,
        meta: {
          title: "盘点单详情",
          access: nodes.inventoryInfoGetStocktakingInfo,
          remixIcon: "home-6-line",
        },
        component: () => import("@/views/stock/WarehouseManagement/InventoryDetail.vue"),
      },
      {
        path: "WarehouseManagement/AllocationForm",
        name: "AllocationForm",
        meta: {
          title: "调拨单",
          access: nodes.AllocationForm,
          remixIcon: "home-6-line",
        },
        component: () => import("@/views/stock/WarehouseManagement/AllocationForm.vue"),
      },
      {
        path: "WarehouseManagement/AddAllocationForm",
        name: "AddAllocationForm",
        hidden: true,
        meta: {
          title: "新增调拨单",
          access: nodes.AllocationFormAddAllocate,
          remixIcon: "home-6-line",
        },
        component: () => import("@/views/stock/WarehouseManagement/AddAllocationForm.vue"),
      },
      {
        path: "WarehouseManagement/EditAllocationForm/:id",
        name: "EditAllocationForm",
        hidden: true,
        meta: {
          title: "修改调拨单",
          access: nodes.AllocationFormUpdateAllocate,
          remixIcon: "home-6-line",
        },
        component: () => import("@/views/stock/WarehouseManagement/AddAllocationForm.vue"),
      },
      {
        path: "WarehouseManagement/DetailAllocationForm/:id",
        name: "DetailAllocationForm",
        hidden: true,
        meta: {
          title: "调拨单详情",
          access: nodes.AllocationFormGetAllocateInfo,
          remixIcon: "home-6-line",
        },
        component: () => import("@/views/stock/WarehouseManagement/DetailAllocationForm.vue"),
      },
      {
        path: "WarehouseManagement/LossReport",
        name: "LossReport",
        meta: {
          title: "报损单",
          remixIcon: "home-6-line",
          access: nodes.LossReport,
        },
        component: () => import("@/views/stock/WarehouseManagement/LossReport.vue"),
      },
      {
        path: "WarehouseManagement/AddLossReport",
        name: "AddLossReport",
        meta: {
          title: "新增报损单",
          remixIcon: "home-6-line",
          access: nodes.LossReportAddLossReport,
        },
        component: () => import("@/views/stock/WarehouseManagement/AddLossReport.vue"),
      },
      {
        path: "WarehouseManagement/EditLossReport",
        name: "EditLossReport",
        meta: {
          title: "修改报损单",
          remixIcon: "home-6-line",
          access: nodes.LossReportEditLossReport,
        },
        component: () => import("@/views/stock/WarehouseManagement/AddLossReport.vue"),
      },
      {
        path: "WarehouseManagement/LossReportInfo/:id",
        name: "LossReportInfo",
        meta: {
          title: "报损单详情",
          remixIcon: "home-6-line",
          access: nodes.LossReportDetailLossReport,
        },
        component: () => import("@/views/stock/WarehouseManagement/LossReportInfo.vue"),
      },
      {
        path: "summary/GoodsReceiptIssueSummary",
        name: "GoodsReceiptIssueSummary",
        hidden: true,
        meta: {
          title: "商品收发汇总",
          access: nodes.PublishGoodsEditGoods,
          remixIcon: "file-text-line",
        },
        component: () => import("@/views/stock/summary/GoodsReceiptIssueSummary"),
      },
      {
        path: "summary/WarehouseStockSummary",
        name: "WarehouseStockSummary",
        meta: {
          title: "库存汇总",
          access: nodes.inventoryStatistics,
        },
        component: () => import("@/views/stock/summary/WarehouseStockSummary.vue"),
      },
      {
        path: "OrdersForShipment/PickingCenter",
        name: "PickingCenter",
        meta: {
          title: "拣货中心",
          // access: nodes.inventoryStatistics,
        },
        component: () => import("@/views/stock/OrdersForShipment/PickingCenter.vue"),
      },
      {
        path: "OrdersForShipment/PickingDetail",
        name: "PickingDetail",
        meta: {
          title: "待拣货订单明细",
          // access: nodes.inventoryStatistics,
        },
        component: () => import("@/views/stock/OrdersForShipment/PickingDetail.vue"),
      },
      {
        path: "OrdersForShipment/pickingList/:id",
        name: "pickingList",
        meta: {
          title: "拣货单",
          // access: nodes.inventoryStatistics,
        },
        component: () => import("@/views/stock/OrdersForShipment/pickingList.vue"),
      },
      {
        path: "OrdersForShipment/DistributionCenter",
        name: "DistributionCenter",
        meta: {
          title: "配送中心",
          // access: nodes.inventoryStatistics,
        },
        component: () => import("@/views/stock/OrdersForShipment/DistributionCenter.vue"),
      },
      {
        path: "OrdersForShipment/DeliveryDetailsOfGoods",
        name: "DeliveryDetailsOfGoods",
        meta: {
          title: "配送商品明细",
          // access: nodes.inventoryStatistics,
        },
        component: () => import("@/views/stock/OrdersForShipment/DeliveryDetailsOfGoods.vue"),
      },
      {
        path: "OrdersForShipment/SalesOutbound/:id",
        name: "SalesOutbound",
        meta: {
          title: "销售出库单",
          // access: nodes.inventoryStatistics,
        },
        component: () => import("@/views/stock/OrdersForShipment/SalesOutbound.vue"),
      },
      {
        path: "OrdersForShipment/DeliveryReceivedDetails/:id",
        name: "DeliveryReceivedDetails",
        meta: {
          title: "配送待收货明细",
          // access: nodes.inventoryStatistics,
        },
        component: () => import("@/views/stock/OrdersForShipment/DeliveryReceivedDetails.vue"),
      },
      {
        path: "OrdersForShipment/DeliveryToBeReceivedDetails",
        name: "DeliveryToBeReceivedDetails",
        meta: {
          title: "配送待收货明细",
          // access: nodes.inventoryStatistics,
        },
        component: () => import("@/views/stock/OrdersForShipment/DeliveryToBeReceivedDetails.vue"),
      },
      // {
      //   path: "OutIn/inventoryWarning",
      //   name: "inventoryWarning",
      //   meta: {
      //     title: "库存预警",
      //     remixIcon: "home-gear-line",
      //   },
      //   component: () => import("@/views/stock/OutIn/inventoryWarning"),
      // },
      {
        path: "supplier/SupplierInventory",
        name: "SupplierInventory",
        meta: {
          title: "供应商库存",
        },
        component: () => import("@/views/stock/supplier/SupplierInventory.vue"),
      },
      {
        path: "supplier/SupplierInventoryFlowing",
        name: "SupplierInventoryFlowing",
        meta: {
          title: "库存流水",
        },
        component: () => import("@/views/stock/supplier/SupplierInventoryFlowing.vue"),
      },
    ],
  },
  {
    path: "/Finance",
    component: Layout,
    redirect: "/Finance/Receivable/ReceivableList",
    alwaysShow: true,
    meta: {
      title: "财务",
      remixIcon: "money-cny-circle-line",
    },
    children: [
      {
        path: "Receivable/ReceivableList",
        name: "ReceivableList",
        meta: {
          title: "应收单列表",
          access: nodes.ReceivableList,
          remixIcon: "align-bottom",
        },
        component: () => import("@/views/Finance/Receivable/ReceivableList"),
      },
      {
        path: "Receivable/AddReceivable",
        name: "AddReceivable",
        hidden: true,
        meta: {
          title: "新增应收单",
          remixIcon: "align-bottom",
        },
        component: () => import("@/views/Finance/Receivable/AddReceivable.vue"),
      },
      {
        path: "Receivable/ReceiptList",
        name: "ReceiptList",
        meta: {
          title: "收款单列表",
          access: nodes.ReceiptListGetAllReceived,
          remixIcon: "align-bottom",
        },
        component: () => import("@/views/Finance/Receivable/ReceiptList.vue"),
      },
      {
        path: "Receivable/AddReceipt",
        name: "AddReceipt",
        hidden: true,
        meta: {
          title: "新增收款单",
          access: nodes.ReceiptListAddReceived,
          remixIcon: "align-bottom",
        },
        component: () => import("@/views/Finance/Receivable/AddReceipt.vue"),
      },
      {
        path: "Receivable/LookReceipt/:id/:createTime",
        name: "LookReceipt",
        hidden: true,
        meta: {
          title: "收款单详情",
          access: nodes.ReceiptListGetReceivedInfo,
          remixIcon: "align-bottom",
        },
        component: () => import("@/views/Finance/Receivable/LookReceipt.vue"),
      },
      {
        path: "Receivable/editReceipt/:id/:createTime",
        name: "EditReceipt",
        hidden: true,
        meta: {
          title: "编辑收款单",
          access: nodes.ReceiptListGetReceivedInfo,
          remixIcon: "align-bottom",
        },
        component: () => import("@/views/Finance/Receivable/AddReceipt.vue"),
      },
      {
        path: "Receivable/ApplyReceipt",
        name: "ApplyReceipt",
        hidden: true,
        meta: {
          title: "收款申请单",
          remixIcon: "align-bottom",
          access: nodes.ApplyReceipt,
        },
        component: () => import("@/views/Finance/Receivable/ApplyReceipt.vue"),
      },
      {
        path: "Receivable/AddApplyReceipt",
        name: "AddApplyReceipt",
        hidden: true,
        meta: {
          title: "新增收款申请单",
          remixIcon: "align-bottom",
          access: nodes.addApplyReceipt,
        },
        component: () => import("@/views/Finance/Receivable/AddApplyReceipt.vue"),
      },
      {
        path: "Receivable/editApplyReceipt/:id",
        name: "editApplyReceipt",
        hidden: true,
        meta: {
          title: "编辑收款申请单",
          remixIcon: "align-bottom",
          access: nodes.editApplyReceipt,
        },
        component: () => import("@/views/Finance/Receivable/AddApplyReceipt.vue"),
      },
      {
        path: "Receivable/ApplyReceiptInfo/:id",
        name: "ApplyReceiptInfo",
        hidden: true,
        meta: {
          title: "收款申请单详情",
          remixIcon: "align-bottom",
          access: nodes.auditApplyReceipt,
        },
        component: () => import("@/views/Finance/Receivable/ApplyReceiptInfo.vue"),
      },
      {
        path: "Receivable/CustomerBalance",
        name: "CustomerBalance",
        meta: {
          title: "客户往来汇总表",
          access: nodes.CustomerBalanceGetAllCustomerBalance,
          remixIcon: "align-bottom",
        },
        component: () => import("@/views/Finance/Receivable/CustomerBalance.vue"),
      },
      {
        path: "Receivable/CustomerBalanceDetail",
        name: "CustomerBalanceDetail",
        meta: {
          title: "客户往来明细表",
          access: nodes.CustomerBalanceDetailGetAllCustomerBalanceDetail,
          remixIcon: "align-bottom",
        },
        component: () => import("@/views/Finance/Receivable/CustomerBalanceDetail.vue"),
      },
      {
        path: "Handle/HandleList",
        name: "HandleList",
        meta: {
          title: "应付单列表",
          access: nodes.HandleList,
          remixIcon: "align-top",
        },
        component: () => import("@/views/Finance/Handle/HandleList.vue"),
      },
      {
        path: "Handle/PaymentList",
        name: "PaymentList",
        meta: {
          title: "付款单列表",
          access: nodes.PaymentListGetAllPaid,
          remixIcon: "align-top",
        },
        component: () => import("@/views/Finance/Handle/PaymentList.vue"),
      },
      {
        path: "Handle/editPayment/:id/:createTime",
        name: "EditPayment",
        hidden: true,
        meta: {
          title: "编辑付款单",
          access: nodes.ReceiptListGetReceivedInfo,
          remixIcon: "align-top",
        },
        component: () => import("@/views/Finance/Handle/AddPayment.vue"),
      },
      {
        path: "Handle/AddPayment",
        name: "AddPayment",
        hidden: true,
        meta: {
          title: "新增付款单",
          access: nodes.PaymentListAddPaid,
          remixIcon: "align-top",
        },
        component: () => import("@/views/Finance/Handle/AddPayment.vue"),
      },
      {
        path: "Handle/LookPayment/:id/:createTime",
        name: "LookPayment",
        hidden: true,
        meta: {
          title: "付款单详情",
          access: nodes.PaymentListGetPaidInfo,
          remixIcon: "align-top",
        },
        component: () => import("@/views/Finance/Handle/LookPayment.vue"),
      },
      {
        path: "Handle/SupplierBalance",
        name: "SupplierBalance",
        meta: {
          title: "供应商往来汇总表",
          access: nodes.CustomerBalanceGetAllSupplierBalance,
          remixIcon: "align-top",
        },
        component: () => import("@/views/Finance/Handle/SupplierBalance.vue"),
      },
      {
        path: "Handle/SupplierBalanceDetails",
        name: "SupplierBalanceDetails",
        meta: {
          title: "供应商余额明细表",
          access: nodes.SupplierBalanceDetailsGetAllSupplierBalanceDetail,
          remixIcon: "align-top",
        },
        component: () => import("@/views/Finance/Handle/SupplierBalanceDetails.vue"),
      },

      {
        path: "Cashier/FunTransfer",
        name: "FunTransfer",
        meta: {
          title: "资金转账单",
          access: nodes.FunTransfer,
          remixIcon: "bank-card-line",
        },
        component: () => import("@/views/Finance/Cashier/FunTransfer.vue"),
      },
      {
        path: "Cashier/AddFunTransfer",
        name: "AddFunTransfer",
        hidden: true,
        meta: {
          title: "新增资金转账单",
          access: nodes.FunTransferAddAccountTransfer,
          remixIcon: "bank-card-line",
        },
        component: () => import("@/views/Finance/Cashier/AddFunTransfer.vue"),
      },
      {
        path: "Cashier/EditFunTransfer/:id",
        name: "EditFunTransfer",
        hidden: true,
        meta: {
          title: "修改资金转账单",
          access: nodes.AccountListEditAccountTransfer,
          remixIcon: "bank-card-line",
        },
        component: () => import("@/views/Finance/Cashier/AddFunTransfer.vue"),
      },
      {
        path: "Cashier/SellRefundForm/:type",
        name: "RefundForm",
        meta: {
          title: "销售退款单",
          access: nodes.RefundForm,
        },
        component: () => import("@/views/Finance/Cashier/RefundForm.vue"),
      },
      // {
      //   path: "Cashier/PurchaseRefundForm/:type",
      //   name: "RefundForm",
      //   meta: {
      //     title: "采购退款单",
      //     access: nodes.RefundForm,
      //   },
      //   component: () => import("@/views/Finance/Cashier/RefundForm.vue"),
      // },
      {
        path: "Cashier/AddRefundForm/:type",
        name: "AddRefundForm",
        meta: {
          title: "新增退款单",
          access: nodes.RefundForm_AddRefundForm,
        },
        component: () => import("@/views/Finance/Cashier/AddRefundForm.vue"),
      },
      {
        path: "Cashier/EditRefundForm/:id/:createTime",
        name: "EditRefundForm",
        meta: {
          title: "编辑退款单",
          access: nodes.RefundFormEditRefundForm,
        },
        component: () => import("@/views/Finance/Cashier/AddRefundForm.vue"),
      },
      {
        path: "Cashier/RefundDetail/:id/:createTime",
        name: "RefundDetail",
        meta: {
          title: "退款单详情",
          // access: nodes.AccountList,
        },
        component: () => import("@/views/Finance/Cashier/RefundDetail.vue"),
      },
      {
        path: "Cashier/AccountList",
        name: "AccountList",
        meta: {
          title: "资金账户管理",
          access: nodes.AccountList,
          remixIcon: "bank-card-line",
        },
        component: () => import("@/views/Finance/Cashier/AccountList.vue"),
      },
      {
        path: "Cashier/AccountDetails",
        name: "AccountDetails",
        meta: {
          title: "账户明细查询",
          access: nodes.AccountDetailsGetAllAccountDetail,
          remixIcon: "bank-card-line",
        },
        component: () => import("@/views/Finance/Cashier/AccountDetails.vue"),
      },
      {
        path: "Cashier/CostSheet",
        name: "CostSheet",
        meta: {
          title: "费用单",
          remixIcon: "bank-card-line",
          access: nodes.CostSheet,
        },
        component: () => import("@/views/Finance/Cashier/CostSheet.vue"),
      },
      {
        path: "Cashier/AddCostSheet",
        name: "AddCostSheet",
        meta: {
          title: "新增费用单",
          remixIcon: "bank-card-line",
          access: nodes.CostSheetaddCostSheet,
        },
        component: () => import("@/views/Finance/Cashier/AddCostSheet.vue"),
      },
      {
        path: "Cashier/EditCostSheet/:id",
        name: "EditCostSheet",
        meta: {
          title: "编辑费用单",
          access: nodes.CostSheeteditCostSheet,
          remixIcon: "bank-card-line",
        },
        component: () => import("@/views/Finance/Cashier/AddCostSheet.vue"),
      },
      {
        path: "Cashier/CostSheetParticulars/:id",
        name: "CostSheetParticulars",
        meta: {
          title: "费用单详情",
          // access: nodes.CostSheeteditCostSheet,
          remixIcon: "bank-card-line",
        },
        component: () => import("@/views/Finance/Cashier/CostSheetParticulars.vue"),
      },
      {
        path: "Cashier/CostTypes",
        name: "CostTypes",
        meta: {
          title: "费用类型",
          remixIcon: "bank-card-line",
        },
        component: () => import("@/views/Finance/Cashier/CostTypes.vue"),
      },
      {
        path: "ManageF/FinanceType",
        name: "FinanceType",
        meta: {
          title: "财务类型",
          access: nodes.FinanceTypeGetAllFinanceType,
          remixIcon: "bookmark-line",
        },
        component: () => import("@/views/Finance/ManageF/FinanceType.vue"),
      },
      {
        path: "ManageF/WithdrawAsh",
        name: "WithdrawAsh",
        meta: {
          title: "余额提现",
          remixIcon: "bookmark-line",
          access: nodes.WithdrawAsh,
        },
        component: () => import("@/views/Finance/ManageF/WithdrawAsh.vue"),
      },
      // 多门店报表
      {
        path: "MultipleStore/InAndOutLogs",
        name: "InAndOutLogs",
        meta: {
          title: "收支记录",
          remixIcon: "money-cny-box-line",
          // access: nodes.WithdrawAsh,
        },
        component: () => import("@/views/Finance/MultipleStore/InAndOutLogs.vue"),
      },
      {
        path: "MultipleStore/WithdrawLogs",
        name: "WithdrawLogs",
        meta: {
          title: "提现记录",
          remixIcon: "money-cny-box-line",
          // access: nodes.WithdrawAsh,
        },
        component: () => import("@/views/Finance/MultipleStore/WithdrawLogs.vue"),
      },
      {
        path: "Supplier/SupplierPaymentList",
        name: "SupplierPaymentList",
        meta: {
          title: "供应商付款单列表",
          remixIcon: "money-cny-box-line",
          // access: nodes.WithdrawAsh,
        },
        component: () => import("@/views/Finance/Supplier/SupplierPaymentList.vue"),
      },
      {
        path: "Supplier/SupplierLookPayment/:id/:createTime",
        name: "SupplierLookPayment",
        meta: {
          title: "供应商付款单详情",
        },
        component: () => import("@/views/Finance/Supplier/SupplierLookPayment.vue"),
      },
    ],
  },
  {
    path: "/Settlement",
    component: Layout,
    redirect: "/Settlement/MerchandiseSalesStatement",
    alwaysShow: true,
    meta: {
      title: "结算",
      remixIcon: "price-tag-2-line",
    },
    children: [
      {
        path: "SettlementLogs",
        name: "SettlementLogs",
        meta: {
          title: "结算记录",
          // access: nodes.WithdrawAsh,
        },
        component: () => import("@/views/Finance/MultipleStore/SettlementLogs.vue"),
      },
    ],
  },
  {
    path: "/Reconciliation",
    component: Layout,
    redirect: "/Reconciliation/SettlementStatement",
    alwaysShow: true,
    meta: {
      title: "对账",
      remixIcon: "money-cny-circle-line",
    },
    children: [
      {
        path: "SettlementStatement",
        name: "SettlementStatement",
        meta: {
          title: "结算对账单",
          // access: nodes.WithdrawAsh,
        },
        component: () => import("@/views/Finance/MultipleStore/SettlementStatement.vue"),
      },
    ],
  },
  {
    path: "/statement",
    component: Layout,
    redirect: "/statement/statementList/MerchandiseSalesStatement",
    alwaysShow: true,
    meta: {
      title: "报表管理",
      remixIcon: "money-cny-circle-line",
    },
    children: [
      {
        path: "statementList/MerchandiseSalesStatement",
        name: "MerchandiseSalesStatement",
        meta: {
          title: "商品销售报表",
          remixIcon: "align-bottom",
          access: nodes.goodsFormMerchandiseSalesStatement,
        },
        component: () => import("@/views/statement/statementList/MerchandiseSalesStatement"),
      },
      {
        path: "statementList/CustomerMerchandiseReport",
        name: "CustomerMerchandiseReport",
        meta: {
          title: "客户商品报表",
          remixIcon: "align-bottom",
          access: nodes.goodsFormCustomerMerchandiseReport,
        },
        component: () => import("@/views/statement/statementList/CustomerMerchandiseReport"),
      },
      {
        path: "statementList/CustomerOrderReport",
        name: "CustomerOrderReport",
        meta: {
          title: "客户订单报表",
          remixIcon: "align-bottom",
          access: nodes.orderFormCustomerOrderReport,
        },
        component: () => import("@/views/statement/statementList/CustomerOrderReport"),
      },
      {
        path: "statementList/OrderDataReport",
        name: "OrderDataReport",
        meta: {
          title: "订单数据报表",
          remixIcon: "align-bottom",
          access: nodes.orderFormOrderDataReport,
        },
        component: () => import("@/views/statement/statementList/OrderDataReport"),
      },
      {
        path: "statementList/RegionalOrderReport",
        name: "RegionalOrderReport",
        meta: {
          title: "地区订单报表",
          remixIcon: "align-bottom",
          access: nodes.orderFormRegionalOrderReport,
        },
        component: () => import("@/views/statement/statementList/RegionalOrderReport"),
      },
      {
        path: "statementList/SaleRanking",
        name: "SaleRanking",
        meta: {
          title: "业务员订单表",
          remixIcon: "align-bottom",
          access: nodes.orderFormSalesOrderReport,
        },
        component: () => import("@/views/statement/statementList/SaleRanking"),
      },
    ],
  },
  {
    path: "/SystemSettings",
    component: Layout,
    redirect: "/SystemSettings/liansuoguanli/ShopList",
    alwaysShow: true,
    meta: {
      title: "设置",
      remixIcon: "settings-4-line",
    },
    children: [
      {
        path: "BaseSet",
        name: "BaseSet",
        meta: {
          title: "系统设置",
          access: nodes.mallManage,
          remixIcon: "settings-4-line",
        },
        component: () => import("@/views/ShoppingMall/mendianset/BaseSet.vue"),
      },
      // {
      //   path: "TradeSet",
      //   name: "TradeSet",
      //   meta: {
      //     title: "交易设置",
      //     access: nodes.mallManagepaySetting,
      //     remixIcon: "settings-4-line",
      //   },
      //   component: () => import("@/views/ShoppingMall/mendianset/BaseSet.vue"),
      // },
      // {
      //   path: "GoodsSet",
      //   name: "GoodsSet",
      //   meta: {
      //     title: "商品设置",
      //     access: nodes.mallManageGoodsSet,
      //     remixIcon: "settings-4-line",
      //   },
      //   component: () => import("@/views/ShoppingMall/mendianset/BaseSet.vue"),
      // },
      // {
      //   path: "CustomerSet",
      //   name: "CustomerSet",
      //   meta: {
      //     title: "客户设置",
      //     access: nodes.mallManageCustomerSet,
      //     remixIcon: "settings-4-line",
      //   },
      //   component: () => import("@/views/ShoppingMall/mendianset/BaseSet.vue"),
      // },
      {
        path: "PrinterSet",
        name: "PrinterSet",
        meta: {
          title: "打印机设置",
          access: nodes.mallManagePrinterSet,
          remixIcon: "settings-4-line",
        },
        component: () => import("@/views/ShoppingMall/mendianset/BaseSet.vue"),
      },
      {
        path: "StepSet",
        name: "StepSet",
        meta: {
          title: "流程设置",
          access: nodes.StepSet,
          remixIcon: "equalizer-line",
        },
        component: () => import("@/views/ShoppingMall/mendianset/StepSet.vue"),
      },
      {
        path: "liansuoguanli/ShopList",
        name: "ShopList",
        meta: {
          title: "商铺列表",
          access: nodes.ShopList,
          remixIcon: "list-settings-line",
        },
        component: () => import("@/views/SystemSettings/liansuoguanli/ShopList.vue"),
      },
      {
        path: "liansuoguanli/AddShop",
        name: "AddShop",
        hidden: true,
        meta: {
          title: "新建商铺",
          access: nodes.ShopListAddShop,
          remixIcon: "list-settings-line",
        },
        component: () => import("@/views/SystemSettings/liansuoguanli/AddShop.vue"),
      },
      {
        path: "liansuoguanli/EditShop/:id",
        name: "EditShop",
        hidden: true,
        meta: {
          title: "编辑商铺",
          access: nodes.ShopListEditShop,
          remixIcon: "list-settings-line",
        },
        component: () => import("@/views/SystemSettings/liansuoguanli/AddShop.vue"),
      },
      {
        path: "liansuoguanli/Partnership",
        name: "Partnership",
        hidden: true,
        meta: {
          title: "合作管理",
          // access: nodes.Partnership,
        },
        component: () => import("@/views/SystemSettings/liansuoguanli/Partnership.vue"),
      },
      {
        path: "liansuoguanli/AddPartnership",
        name: "AddPartnership",
        hidden: true,
        meta: {
          title: "新增合作",
          // access: nodes.PartnershipAddShopPartner,
        },
        component: () => import("@/views/SystemSettings/liansuoguanli/AddPartnership.vue"),
      },
      {
        path: "liansuoguanli/EditPartnership/:id",
        name: "EditPartnership",
        hidden: true,
        meta: {
          title: "编辑合作",
          // access: nodes.PartnershipUpdateShopPartner,
        },
        component: () => import("@/views/SystemSettings/liansuoguanli/AddPartnership.vue"),
      },

      {
        path: "jiaoyiset/PayList",
        name: "PayList",
        meta: {
          title: "支付列表",
          access: nodes.PayList,
          remixIcon: "wallet-line",
        },
        component: () => import("@/views/SystemSettings/jiaoyiset/PayList.vue"),
      },
      {
        path: "jiaoyiset/Driver",
        name: "Driver",
        meta: {
          title: "司机列表",
          access: nodes.Driver,
          remixIcon: "wallet-line",
        },
        component: () => import("@/views/SystemSettings/jiaoyiset/Driver.vue"),
      },
      {
        path: "jiaoyiset/Line",
        name: "Line",
        meta: {
          title: "线路列表",
          access: nodes.line,
          remixIcon: "wallet-line",
        },
        component: () => import("@/views/SystemSettings/jiaoyiset/Line.vue"),
      },
      {
        path: "jiaoyiset/Delivery",
        name: "Delivery",
        meta: {
          title: "配送方式",
          access: nodes.DeliverySet,
          remixIcon: "truck-line",
        },
        component: () => import("@/views/SystemSettings/jiaoyiset/DeliverySet.vue"),
      },
      {
        path: "jiaoyiset/DeliveryTemplate/:deiId",
        name: "DeliveryTemplate",
        hidden: true,
        meta: {
          title: "运费模版",
          access: nodes.DeliverygetInfo,
          remixIcon: "truck-line",
        },
        component: () => import("@/views/SystemSettings/jiaoyiset/DeliveryTemplate.vue"),
      },
      {
        path: "jiaoyiset/AddDeliveryTem",
        name: "AddDeliveryTem",
        hidden: true,
        meta: {
          title: "新建运费模版",
          access: nodes.DeliverysetData,
          remixIcon: "truck-line",
        },
        component: () => import("@/views/SystemSettings/jiaoyiset/AddDeliveryTem.vue"),
      },
      {
        path: "jiaoyiset/EditDeliveryTem",
        name: "EditDeliveryTem",
        hidden: true,
        meta: {
          title: "编辑运费模版",
          access: nodes.DeliverysetData,
          remixIcon: "truck-line",
        },
        component: () => import("@/views/SystemSettings/jiaoyiset/AddDeliveryTem.vue"),
      },
      {
        path: "jiaoyiset/SelffetchList/:deiId",
        name: "SelffetchList",
        hidden: true,
        meta: {
          title: "门店自提",
          remixIcon: "truck-line",
          access: nodes.DeliverygetInfo,
        },
        component: () => import("@/views/SystemSettings/jiaoyiset/SelffetchList.vue"),
      },
      {
        path: "jiaoyiset/AddSelffetch/:deiId",
        name: "AddSelffetch",
        hidden: true,
        meta: {
          title: "新建自提点",
          remixIcon: "truck-line",
          access: nodes.DeliverySetsetSelfData,
        },
        component: () => import("@/views/SystemSettings/jiaoyiset/AddSelffetch.vue"),
      },
      {
        path: "jiaoyiset/EditSelffetch/:id/:deiId",
        name: "EditSelffetch",
        hidden: true,
        meta: {
          title: "编辑自提点",
          access: nodes.DeliverySeteditSelfData,
        },
        component: () => import("@/views/SystemSettings/jiaoyiset/AddSelffetch.vue"),
      },
      {
        path: "jiaoyiset/Dispatch",
        name: "Dispatch",
        hidden: true,
        meta: {
          title: "同城配送",
          remixIcon: "truck-line",
        },
        component: () => import("@/views/SystemSettings/jiaoyiset/Dispatch.vue"),
      },
      {
        path: "jiaoyiset/ShopAddress",
        name: "ShopAddress",
        hidden: true,
        meta: {
          title: "编辑商家地址",
        },
        component: () => import("@/views/SystemSettings/jiaoyiset/ShopAddress.vue"),
      },
      {
        path: "jiaoyiset/Logistics",
        name: "Logistics",
        meta: {
          title: "物流接口",
          remixIcon: "truck-line",
          access: nodes.Logistics,
        },
        component: () => import("@/views/SystemSettings/jiaoyiset/Logistics.vue"),
      },
      {
        path: "jiaoyiset/voiceSet",
        name: "VoiceSet",
        meta: {
          title: "语音设置",
          access: nodes.voiceSet,
          remixIcon: "notification-3-line",
        },
        component: () => import("@/views/SystemSettings/jiaoyiset/voiceSet.vue"),
      },
      {
        path: "jiaoyiset/PushNotification",
        name: "PushNotification",
        meta: {
          title: "消息推送",
          remixIcon: "notification-3-line",
          access: nodes.PushNotification,
        },
        component: () => import("@/views/SystemSettings/jiaoyiset/PushNotification.vue"),
      },
      {
        path: "jiaoyiset/PushNotificationSet/:id",
        name: "PushNotificationSet",
        meta: {
          title: "消息推送设置",
          remixIcon: "notification-3-line",
          access: nodes.PushNotification_setMsg,
        },
        component: () => import("@/views/SystemSettings/jiaoyiset/PushNotificationSet.vue"),
      },
      {
        path: "jiaoyiset/SMSConfiguration",
        name: "SMSConfiguration",
        meta: {
          title: "短信配置",
          remixIcon: "notification-3-line",
        },
        component: () => import("@/views/SystemSettings/jiaoyiset/SMSConfiguration.vue"),
      },
      {
        path: "jiaoyiset/SMSConfigurationSet",
        name: "SMSConfigurationSet",
        meta: {
          title: "配置",
          remixIcon: "notification-3-line",
        },
        component: () => import("@/views/SystemSettings/jiaoyiset/SMSConfigurationSet.vue"),
      },
      {
        path: "accountAdmin/Department",
        name: "Department",
        meta: {
          title: "部门管理",
          access: nodes.Department,
          remixIcon: "user-settings-line",
        },
        component: () => import("@/views/SystemSettings/accountAdmin/Department.vue"),
      },
      {
        path: "accountAdmin/Role",
        name: "Role",
        meta: {
          title: "角色管理",
          access: nodes.Role,
          remixIcon: "user-settings-line",
        },
        component: () => import("@/views/SystemSettings/accountAdmin/Role.vue"),
      },
      {
        path: "accountAdmin/Staff",
        name: "Staff",
        meta: {
          title: "员工管理",
          access: nodes.Staff,
          remixIcon: "user-settings-line",
        },
        component: () => import("@/views/SystemSettings/accountAdmin/Staff.vue"),
      },
      {
        path: "accountAdmin/LoginRecord",
        name: "LoginRecord",
        meta: {
          title: "登录日志",
          access: nodes.LoginRecord,
        },
        component: () => import("@/views/SystemSettings/accountAdmin/LoginRecord.vue"),
      },
    ],
  },
  {
    path: "ShoppingMall",
    component: Layout,
    redirect: "/ShoppingMall/mendianset/BaseSet",
    alwaysShow: true,
    meta: {
      title: "商城",
      remixIcon: "store-2-line",
    },
    children: [
      {
        path: "mendianset/NoticeLsit",
        name: "NoticeLsit",
        meta: {
          title: "公告设置",
          access: nodes.Announcement,
          remixIcon: "notification-3-line",
        },
        component: () => import("@/views/ShoppingMall/mendianset/NoticeLsit.vue"),
      },
      {
        path: "mendianset/NewsTemplate",
        name: "NewsTemplate",
        meta: {
          title: "小程序订阅消息",
          access: nodes.Announcement,
          remixIcon: "notification-3-line",
        },
        component: () => import("@/views/ShoppingMall/mendianset/NewsTemplate.vue"),
      },
      {
        path: "WxCode/WxCodeSet",
        name: "WxCodeSet",
        meta: {
          title: "小程序设置",
          access: nodes.WxCodeSet,
          remixIcon: "mini-program-line",
        },
        component: () => import("@/views/ShoppingMall/WxCode/WxCodeSet.vue"),
      },
      {
        path: "WxCode/TemplateSet",
        name: "TemplateSet",
        meta: {
          title: "小程序发布",
          access: nodes.wxPush,
          remixIcon: "mini-program-line",
        },
        component: () => import("@/views/ShoppingMall/WxCode/TemplateSet.vue"),
      },
      {
        path: "WxCode/TouTiaoBaseSet",
        name: "TouTiaoBaseSet",
        hidden: true,
        meta: {
          title: "抖音/头条小程序基础配置",
          access: nodes.TemplateSet,
          remixIcon: "mini-program-line",
        },
        component: () => import("@/views/ShoppingMall/WxCode/ToutiaoCodeSet/TouTiaoBaseSet.vue"),
      },
      {
        path: "WxCode/CodeRelease",
        name: "CodeRelease",
        hidden: true,
        meta: {
          title: "抖音/头条小程序发布",
          access: nodes.TemplateSet,
          remixIcon: "mini-program-line",
        },
        component: () => import("@/views/ShoppingMall/WxCode/ToutiaoCodeSet/CodeRelease.vue"),
      },
      {
        path: "AppDesign/PageDesign",
        name: "PageDesign",
        hidden: true,
        meta: {
          title: "新建页面",
          remixIcon: "brush-3-line",
          access: nodes.PageSetSave,
        },
        component: () => import("@/views/ShoppingMall/AppDesign/PageDesign.vue"),
      },
      {
        path: "AppDesign/PageDesignEdit/:id",
        name: "PageDesignEdit",
        hidden: true,
        meta: {
          title: "修改页面",
          remixIcon: "brush-3-line",
          access: nodes.PageSetSave,
        },
        component: () => import("@/views/ShoppingMall/AppDesign/PageDesign.vue"),
      },
      {
        path: "AppDesign/PageDesignList",
        name: "PageDesignList",
        meta: {
          title: "页面设计",
          access: nodes.PageSet,
          remixIcon: "brush-3-line",
        },
        component: () => import("@/views/ShoppingMall/AppDesign/PageDesignList.vue"),
      },
      {
        path: "AppDesign/StartUpPage",
        name: "StartUpPage",
        meta: {
          title: "启动页",
          access: nodes.StartPage,
          remixIcon: "brush-3-line",
        },
        component: () => import("@/views/ShoppingMall/AppDesign/StartUpPage.vue"),
      },
      {
        path: "AppDesign/AppStyle",
        name: "AppStyle",
        meta: {
          title: "风格设置",
          remixIcon: "brush-3-line",
          access: nodes.StyleSet,
        },
        component: () => import("@/views/ShoppingMall/AppDesign/AppStyle.vue"),
      },
      {
        path: "AppDesign/CateSet",
        name: "CateSet",
        meta: {
          title: "分类模版",
          access: nodes.CategorySet,
          remixIcon: "brush-3-line",
        },
        component: () => import("@/views/ShoppingMall/AppDesign/CateSet.vue"),
      },
    ],
  },
  {
    path: "Application",
    component: Layout,
    redirect: "/Application/Application",
    alwaysShow: true,
    meta: {
      title: "应用",
      remixIcon: "app-store-line",
    },
    children: [
      {
        path: "Application",
        name: "Application",
        component: () => import("@/views/Application/Application"),
        meta: {
          title: "应用",
          remixIcon: "app-store-line",
        },
      },
    ],
  },
  {
    path: "MoneyGoodsBill",
    component: Layout,
    redirect: "/MoneyGoodsBill/index",
    alwaysShow: true,
    meta: {
      title: "钱货日清对账",
      remixIcon: "app-store-line",
    },
    children: [
      {
        path: "index",
        name: "InventoryStatistics",
        component: () => import("@/views/MoneyGoodsBill/index"),
        meta: {
          title: "库存日对账",
          remixIcon: "hotel-line",
          access: nodes.getTodayStatistics_statisticsInventoryStatistics,
        },
      },
      {
        path: "saleBill",
        name: "saleBill",
        component: () => import("@/views/MoneyGoodsBill/saleBill"),
        meta: {
          title: "销售日对账",
          remixIcon: "numbers-line",
          access: nodes.getTodayStatistics_statisticsGetAllOrderData,
        },
      },
      {
        path: "FinanceBill",
        name: "FinanceBill",
        component: () => import("@/views/MoneyGoodsBill/FinanceBill"),
        meta: {
          title: "财务日对账",
          remixIcon: "stack-line",
          access: nodes.getTodayStatistics_statisticsGetTodayStatistics,
        },
      },
      {
        path: "Profit",
        name: "Profit",
        component: () => import("@/views/MoneyGoodsBill/Profit"),
        meta: {
          title: "利润表",
          remixIcon: "stack-line",
        },
      },
    ],
  },
  {
    path: "Marketing",
    component: Layout,
    redirect: "/Marketing/MarketingList",
    alwaysShow: true,
    hidden: true,
    meta: {
      title: "营销",
      remixIcon: "coupon-3-line",
    },
    children: [
      {
        path: "MarketingList/Coupon",
        name: "Coupon",
        meta: {
          title: "优惠券列表",
          remixIcon: "coupon-3-line",
          access: nodes.CouponList,
        },
        component: () => import("@/views/Marketing/MarketingList/Coupon"),
      },
      {
        path: "MarketingList/ReleaseRecord",
        name: "ReleaseRecord",
        meta: {
          title: "发放记录",
          access: nodes.GrantLog,
        },
        component: () => import("@/views/Marketing/MarketingList/ReleaseRecord"),
      },
      {
        path: "MarketingList/AddCoupon",
        name: "AddCoupon",
        hidden: true,
        meta: {
          title: "新增优惠券",
          access: nodes.CouponListAdd,
        },
        component: () => import("@/views/Marketing/MarketingList/AddCoupon"),
      },
      {
        path: "MarketingList/EditCoupon/:id",
        name: "EditCoupon",
        hidden: true,
        meta: {
          title: "编辑优惠券",
          access: nodes.CouponListEdit,
        },
        component: () => import("@/views/Marketing/MarketingList/AddCoupon"),
      },
      {
        path: "Promotion/PromotionList",
        name: "PromotionList",
        meta: {
          title: "商品促销",
          remixIcon: "bookmark-3-line",
          access: nodes.ActivityGoods,
        },
        component: () => import("@/views/Marketing/Promotion/PromotionList.vue"),
      },
      {
        path: "Promotion/AddPromotion",
        name: "AddPromotion",
        hidden: true,
        meta: {
          title: "新增促销",
          access: nodes.ActivityGoodsAdd,
        },
        component: () => import("@/views/Marketing/Promotion/AddPromotion.vue"),
      },
      {
        path: "Promotion/editPromotion/:id",
        name: "EditPromotion",
        hidden: true,
        meta: {
          title: "修改促销",
          access: nodes.ActivityGoodsEdit,
        },
        component: () => import("@/views/Marketing/Promotion/AddPromotion.vue"),
      },

      {
        path: "vip/membershipCard",
        name: "MembershipCard",
        meta: {
          title: "会员卡管理",
          remixIcon: "vip-crown-2-line",
          access: nodes.MembershipCard,
        },
        component: () => import("@/views/Marketing/vip/membershipCard.vue"),
      },
      {
        path: "vip/AddCard",
        name: "AddCard",
        hidden: true,
        meta: {
          title: "创建会员卡",
          access: nodes.MembershipCardAddVipCard,
        },
        component: () => import("@/views/Marketing/vip/AddCard.vue"),
      },
      {
        path: "vip/EditCard/:id",
        name: "EditCard",
        hidden: true,
        meta: {
          title: "编辑会员卡",
          access: nodes.MembershipCardEditVipCard,
        },
        component: () => import("@/views/Marketing/vip/AddCard.vue"),
      },
      {
        path: "vip/CollectionRecords",
        name: "CollectionRecords",
        meta: {
          title: "领取记录",
          access: nodes.CollectionRecords,
        },
        component: () => import("@/views/Marketing/vip/CollectionRecords.vue"),
      },
      {
        path: "vip/DelRecords",
        name: "DelRecords",
        hidden: true,
        meta: {
          title: "删除记录",
        },
        component: () => import("@/views/Marketing/vip/DelRecords.vue"),
      },
      {
        path: "SetMeal/index",
        name: "SetMeal",
        hidden: true,
        meta: {
          title: "组合套餐",
          access: nodes.Application_SetMeal,
          remixIcon: "git-repository-private-line",
        },
        component: () => import("@/views/Marketing/SetMeal/index"),
      },
      {
        path: "SetMeal/addSetMeal",
        name: "AddSetMeal",
        hidden: true,
        meta: {
          title: "新增活动",
          access: nodes.SetMeal_addGroupGoods,
        },
        component: () => import("@/views/Marketing/SetMeal/addSetMeal"),
      },
      {
        path: "SetMeal/editSetMeal/:id",
        name: "EditSetMeal",
        hidden: true,
        meta: {
          title: "编辑活动",
          access: nodes.SetMeal_editGroupGoods,
        },
        component: () => import("@/views/Marketing/SetMeal/addSetMeal"),
      },
      {
        path: "FullGive/FullGiveList",
        name: "FullGiveList",
        meta: {
          title: "满赠列表",
          access: nodes.FullGive_getAllFullGive,
        },
        component: () => import("@/views/Marketing/FullGive/FullGiveList"),
      },
      {
        path: "FullGive/AddFullGive",
        name: "AddFullGive",
        hidden: true,
        meta: {
          title: "新增满赠",
          access: nodes.FullGive_addFullGive,
        },
        component: () => import("@/views/Marketing/FullGive/AddFullGive"),
      },
      {
        path: "FullGive/EditFullGive/:id",
        name: "EditFullGive",
        hidden: true,
        meta: {
          title: "编辑满赠",
          access: nodes.FullGive_editFullGive,
        },
        component: () => import("@/views/Marketing/FullGive/AddFullGive"),
      },
      {
        path: "FullBuy/FullBuyList",
        name: "FullBuyList",
        meta: {
          title: "满额换购",
          access: nodes.FullBuy_getAllFullBuy,
        },
        component: () => import("@/views/Marketing/FullBuy/FullBuyList"),
      },
      {
        path: "FullBuy/AddFullBuy",
        name: "AddFullBuy",
        hidden: true,
        meta: {
          title: "新增满额换购",
          access: nodes.FullBuy_addFullBuy,
        },
        component: () => import("@/views/Marketing/FullBuy/AddFullBuy"),
      },
      {
        path: "FullBuy/EditFullBuy/:id",
        name: "EditFullBuy",
        hidden: true,
        meta: {
          title: "编辑满额换购",
          access: nodes.FullBuy_editFullBuy,
        },
        component: () => import("@/views/Marketing/FullBuy/AddFullBuy"),
      },
      {
        path: "FullBuy/ViewFullBuy/:id",
        name: "ViewFullBuy",
        hidden: true,
        meta: { title: "查看满额换购", access: nodes.FullBuy_getAllFullBuy },
        component: () => import("@/views/Marketing/FullBuy/ViewFullBuy"),
      },
    ],
  },
  {
    path: "saleCommission",
    component: Layout,
    redirect: "/saleCommission/CommissionRule",
    alwaysShow: true,
    hidden: true,
    meta: {
      title: "销售提成",
      remixIcon: "store-2-line",
    },
    children: [
      {
        path: "CommissionRule",
        name: "CommissionRule",
        component: () => import("@/views/saleCommission/CommissionRule"),
        meta: {
          title: "提成规则",
          access: nodes.CommissionRule,
        },
      },
      {
        path: "CommissionStatistic",
        name: "CommissionStatistic",
        component: () => import("@/views/saleCommission/CommissionStatistic"),
        meta: {
          title: "提成统计",
          access: nodes.CommissionStatistic,
        },
      },
      {
        path: "RuleCommission",
        name: "RuleCommission",
        component: () => import("@/views/saleCommission/RuleCommission"),
        meta: {
          title: "设置提成规则",
          access: nodes.CommissionRuleAddCommissionRule,
        },
      },
      {
        path: "RuleCommissionEdit",
        name: "RuleCommissionEdit",
        component: () => import("@/views/saleCommission/RuleCommission"),
        meta: {
          title: "修改提成规则",
          access: nodes.CommissionRuleEditCommissionRule,
        },
      },
      // {
      //   path: "CommissionList",
      //   name: "CommissionList",
      //   component: () => import("@/views/saleCommission/CommissionList"),
      //   meta: {
      //     title: "提成记录",
      //   },
      // },
      {
        path: "CommissionList",
        name: "CommissionList",
        component: () => import("@/views/saleCommission/CommissionList"),
        meta: {
          title: "提成记录",
        },
      },
    ],
  },
  {
    path: "PointsMall",
    component: Layout,
    redirect: "/PointsMall/GoodsManage",
    alwaysShow: true,
    hidden: true,
    meta: {
      title: "积分商城",
      remixIcon: "store-2-line",
    },
    children: [
      {
        path: "GoodsManage",
        name: "GoodsManage",
        component: () => import("@/views/PointsMall/GoodsManage"),
        meta: {
          title: "积分商品管理",
          access: nodes.PointsGoodsManage,
        },
      },
      {
        path: "PointGoodsAdd",
        name: "PointGoodsAdd",
        component: () => import("@/views/PointsMall/PointGoodsAdd"),
        meta: {
          title: "新增积分商品",
          access: nodes.PointsGoodsManageAddPointsGoods,
        },
      },
      {
        path: "PointGoodsEdit",
        name: "PointGoodsEdit",
        component: () => import("@/views/PointsMall/PointGoodsAdd"),
        meta: {
          title: "修改积分商品",
          access: nodes.PointsGoodsManageEditPointsGoods,
        },
      },
      {
        path: "ExchangeRecord",
        name: "ExchangeRecord",
        component: () => import("@/views/PointsMall/ExchangeRecord"),
        meta: {
          title: "兑换记录",
          access: nodes.ExchangeRecord,
        },
      },
      {
        path: "PointRule",
        name: "PointRule",
        component: () => import("@/views/PointsMall/PointRule"),
        meta: {
          title: "积分规则",
          access: nodes.PointRule,
        },
      },
      {
        path: "PointRuleAdd",
        name: "PointRuleAdd",
        component: () => import("@/views/PointsMall/PointRuleAdd"),
        meta: {
          title: "新增积分规则",
          access: nodes.PointRuleAddPointRule,
        },
      },
      {
        path: "PointRuleEdit",
        name: "PointRuleEdit",
        component: () => import("@/views/PointsMall/PointRuleAdd"),
        meta: {
          title: "修改积分规则",
          access: nodes.PointRuleEditPointRule,
        },
      },
    ],
  },
  {
    path: "Distribution",
    component: Layout,
    redirect: "/Distribution/DOverview",
    alwaysShow: true,
    hidden: true,
    meta: {
      title: "分销",
      remixIcon: "store-2-line",
    },
    children: [
      {
        path: "DOverview",
        name: "DOverview",
        component: () => import("@/views/Distribution/DOverview"),
        meta: {
          title: "分销概览",
          access: nodes.DOverview,
        },
      },
      {
        path: "DGoodsList",
        name: "DGoodsList",
        component: () => import("@/views/Distribution/DGoodsList"),
        meta: {
          title: "分销商品",
          access: nodes.DGoodsList,
        },
      },
      {
        path: "SetCommission/:id",
        name: "SetCommission",
        hidden: true,
        meta: {
          title: "设置佣金",
          access: nodes.DGoodsListsetCommission,
        },
        component: () => import("@/views/Distribution/Commission"),
      },
      {
        path: "DOrderList",
        name: "DOrderList",
        component: () => import("@/views/Distribution/DOrderList"),
        meta: {
          title: "分销订单",
          access: nodes.DOrderList,
        },
      },
      {
        path: "DistributionList",
        name: "DistributionList",
        component: () => import("@/views/Distribution/DistributionList"),
        meta: {
          title: "分销商等级",
          access: nodes.DistributionList,
        },
      },
      {
        path: "AddDistributionList",
        name: "AddDistributionList",
        hidden: true,
        meta: {
          title: "新建分销商等级",
          access: nodes.DistributionListupdateGrade,
        },
        component: () => import("@/views/Distribution/AddDistributionList"),
      },
      {
        path: "EditDistributionList/:id",
        name: "EditDistributionList",
        hidden: true,
        meta: {
          title: "编辑分销商等级",
          access: nodes.DistributionListupdateGrade,
        },
        component: () => import("@/views/Distribution/AddDistributionList"),
      },

      {
        path: "Businessman/NotAudit",
        name: "NotAudit",
        meta: {
          title: "待审核",
          access: nodes.Businessman,
        },
        component: () => import("@/views/Distribution/Businessman/NotAudit"),
      },
      {
        path: "Businessman/Distributor",
        name: "Distributor",
        meta: {
          title: "分销商",
          access: nodes.Businessman,
        },
        component: () => import("@/views/Distribution/Businessman/Distributor"),
      },
      {
        path: "Businessman/DistributorNext",
        name: "DistributorNext",
        hidden: true,
        meta: {
          title: "下级分销商",
          access: nodes.BusinessmangetgetAllSub,
        },
        component: () => import("@/views/Distribution/Businessman/DistributorNext"),
      },

      {
        path: "CashOut/NotAuditCashOut",
        name: "NotAuditCashOut",
        meta: {
          title: "待审核",
          access: nodes.CashOutgetAll,
        },
        component: () => import("@/views/Distribution/CashOut/NotAuditCashOut"),
      },
      {
        path: "CashOut/NotCashOut",
        name: "NotCashOut",
        meta: {
          title: "待打款",
          access: nodes.CashOutgetAll,
        },
        component: () => import("@/views/Distribution/CashOut/NotAuditCashOut"),
      },
      {
        path: "CashOut/CashOut",
        name: "CashOut",
        meta: {
          title: "已打款",
          access: nodes.CashOutgetAll,
        },
        component: () => import("@/views/Distribution/CashOut/NotAuditCashOut"),
      },
      {
        path: "CashOut/Invalid",
        name: "Invalid",
        meta: {
          title: "无效",
          access: nodes.CashOutgetAll,
        },
        component: () => import("@/views/Distribution/CashOut/NotAuditCashOut"),
      },
      {
        path: "DistributionSet/BaseDSet",
        name: "BaseDSet",
        meta: {
          title: "基础设置",
          access: nodes.SettlementSet,
        },
        component: () => import("@/views/Distribution/DistributionSet/BaseDSet.vue"),
      },
      {
        path: "DistributionSet/SettlementSet",
        name: "SettlementSet",
        meta: {
          title: "结算设置",
          access: nodes.SettlementSet,
        },
        component: () => import("@/views/Distribution/DistributionSet/SettlementSet.vue"),
      },
      {
        path: "DistributionSet/TextSet",
        name: "TextSet",
        meta: {
          title: "文字设置",
          access: nodes.TextSet,
        },
        component: () => import("@/views/Distribution/DistributionSet/TextSet.vue"),
      },
    ],
  },
  {
    path: "BillTemplate",
    component: Layout,
    redirect: "/BillTemplate/TemplateList",
    alwaysShow: true,
    hidden: true,
    meta: {
      title: "单据模版",
      remixIcon: "apps-line",
    },
    children: [
      {
        path: "TemplateList",
        name: "TemplateList",
        meta: {
          title: "单据模板列表",
          access: nodes.TemplateListgetAll,
        },
        component: () => import("@/views/BillTemplate/TemplateList"),
      },
      {
        path: "EditTemplate/:id",
        name: "EditTemplate",
        hidden: true,
        meta: {
          title: "编辑模板",
          access: nodes.TemplateListgetInfo,
        },
        component: () => import("@/views/BillTemplate/EditTemplate"),
      },
    ],
  },
  {
    path: "CashierShop",
    name: "CashierShop",
    noKeepAlive: true,
    hidden: true,
    meta: {
      title: "收银台",
      access: nodes.CashierShop,
      remixIcon: "airplay-fill",
    },
    component: () => import("@/views/CashierShop/CashierShop"),
  },
  {
    path: "CashierShift",
    name: "CashierShift",
    noKeepAlive: true,
    hidden: true,
    meta: {
      title: "收银交班",
      access: nodes.ShiftOvergetRecordInfo,
    },
    component: () => import("@/views/CashierShop/CashierShift"),
  },
  {
    path: "CashierRecord",
    name: "CashierRecord",
    noKeepAlive: true,
    hidden: true,
    meta: {
      title: "交班记录",
      remixIcon: "exchange-box-line",
      access: nodes.ShiftOver,
    },
    component: () => import("@/views/CashierShop/CashierRecord"),
  },
  {
    path: "Cashier",
    component: Layout,
    redirect: "/Cashier/CashierList",
    hidden: true,
    meta: {
      title: "收银台",
      remixIcon: "airplay-fill",
    },
    children: [
      {
        path: "CashierList",
        name: "CashierList",
        meta: {
          title: "收银台设置",
          remixIcon: "settings-5-line",
          access: nodes.cashierSet,
        },
        component: () => import("@/views/Cashier/CashierList"),
      },
      {
        path: "Cashier",
        name: "Cashier",
        meta: {
          title: "收银员",
          remixIcon: "user-2-line",
          access: nodes.Cashiers,
        },
        component: () => import("@/views/Cashier/Cashier"),
      },
      {
        path: "ShoppingGuide",
        name: "ShoppingGuide",
        meta: {
          title: "导购员",
          remixIcon: "user-heart-line",
          access: nodes.ShoppingGuide,
        },
        component: () => import("@/views/Cashier/ShoppingGuide"),
      },
      {
        path: "/order/saleO/CashierOrder",
        name: "CashierOrder",
        meta: {
          title: "收银台订单",
          access: nodes.saleOrderList,
          remixIcon: "bill-line",
        },
        component: () => import("@/views/order/saleO/SaleOrderList"),
      },
      {
        path: "Withdrawal",
        name: "Withdrawal",
        meta: {
          title: "提成明细",
          remixIcon: "file-list-3-line",
          access: nodes.Withdrawal,
        },
        component: () => import("@/views/Cashier/Withdrawal"),
      },
      {
        path: "Commission",
        name: "Commission",
        meta: {
          title: "提成统计",
          remixIcon: "exchange-funds-line",
          access: nodes.Commission,
        },
        component: () => import("@/views/Cashier/Commission"),
      },
      {
        path: "ShiftOver",
        name: "ShiftOver",
        meta: {
          title: "交班记录",
          remixIcon: "exchange-box-line",
          access: nodes.ShiftOver,
        },
        component: () => import("@/views/Cashier/ShiftOver"),
      },
      {
        path: "ShiftOverGetAll/:id",
        name: "ShiftOverGetAll",
        hidden: true,
        meta: {
          title: "交班详情",
          access: nodes.ShiftOvergetRecordInfo,
        },
        component: () => import("@/views/Cashier/ShiftOverGetAll"),
      },
      {
        path: "EditCashier/:id",
        name: "EditCashier",
        hidden: true,
        meta: {
          title: "编辑收银员",
          access: nodes.CashiersupdateStaff,
        },
        component: () => import("@/views/Cashier/EditCashier"),
      },
    ],
  },
  {
    path: "/Merchants",
    redirect: "/Merchants/MerchartsList",
    component: Layout,
    hidden: true,
    meta: {
      title: "多商户",
      remixIcon: "airplay-fill",
    },
    children: [
      {
        path: "MerchartsList",
        name: "MerchartsList",
        meta: {
          title: "商户管理",
          remixIcon: "store-2-line",
          access: nodes.MerchartsList,
        },
        component: () => import("@/views/Merchants/MerchartsList"),
      },
      {
        path: "Merchants",
        name: "Merchants",
        meta: {
          title: "入驻申请",
          remixIcon: "store-2-line",
        },
        component: () => import("@/views/Merchants/Merchants"),
      },
      {
        path: "ApplyMerchantsInfo/:id",
        name: "ApplyMerchantsInfo",
        meta: {
          title: "入驻详情",
          remixIcon: "store-2-line",
        },
        component: () => import("@/views/Merchants/ApplyMerchantsInfo"),
      },
      {
        path: "MerchantsGroup",
        name: "MerchantsGroup",
        meta: {
          title: "商户分组",
          remixIcon: "money-cny-circle-line",
        },
        component: () => import("@/views/Merchants/MerchantsGroup"),
      },
      {
        path: "MerchantsBin",
        name: "MerchantsBin",
        meta: {
          title: "回收站",
          remixIcon: "store-2-line",
        },
        component: () => import("@/views/Merchants/MerchantsBin"),
      },
      {
        path: "AddMerchants",
        name: "AddMerchants",
        meta: {
          title: "添加商户",
          remixIcon: "money-cny-circle-line",
        },
        component: () => import("@/views/Merchants/AddMerchants"),
      },
      {
        path: "EditMerchants",
        name: "EditMerchants",
        meta: {
          title: "编辑商户",
          remixIcon: "money-cny-circle-line",
        },
        component: () => import("@/views/Merchants/AddMerchants"),
      },
      {
        path: "AddMerchantsInfo/:length",
        name: "AddMerchantsInfo",
        meta: {
          title: "增购详情",
          remixIcon: "money-cny-circle-line",
        },
        component: () => import("@/views/Merchants/AddMerchantsInfo"),
      },
      {
        path: "MerchantsInfo/:id",
        name: "MerchantsInfo",
        meta: {
          title: "商户详情",
          remixIcon: "store-2-line",
        },
        component: () => import("@/views/Merchants/MerchantsInfo"),
      },
      {
        path: "MerchartsShop",
        name: "MerchartsShop",
        meta: {
          title: "商户商品",
          remixIcon: "shopping-bag-line",
          access: nodes.MerchartsGoods,
        },
        component: () => import("@/views/Merchants/MerchartsShop"),
      },
      {
        path: "MerchantsExamine",
        name: "MerchantsExamine",
        meta: {
          title: "待审核商品",
          remixIcon: "shopping-bag-line",
        },
        component: () => import("@/views/Merchants/MerchantsExamine"),
      },
      {
        path: "MerchantsOverview",
        name: "MerchantsOverview",
        meta: {
          title: "商户概览",
          remixIcon: "funds-box-line",
        },
        component: () => import("@/views/Merchants/MerchantsOverview"),
      },
      {
        path: "MerchantsOrder",
        name: "MerchantsOrder",
        meta: {
          title: "商户订单",
          remixIcon: "list-unordered",
        },
        component: () => import("@/views/Merchants/MerchantsOrder"),
      },
      {
        path: "SafeguardingOrder",
        name: "SafeguardingOrder",
        meta: {
          title: "维权订单",
          remixIcon: "list-unordered",
        },
        component: () => import("@/views/Merchants/SafeguardingOrder"),
      },
      {
        path: "MerchantsSettlement",
        name: "MerchantsSettlement",
        meta: {
          title: "结算管理",
          remixIcon: "secure-payment-line",
          access: nodes.MerchantsSettlement,
        },
        component: () => import("@/views/Merchants/Settlement"),
      },
      {
        path: "MerchantsBasicsSet",
        name: "MerchantsBasicsSet",
        meta: {
          title: "基础设置",
          remixIcon: "settings-4-line",
          access: nodes.MerchantsSettlementMerchantsBasicsSet,
        },
        component: () => import("@/views/Merchants/MerchantsBasicsSet"),
      },
      {
        path: "MerchantsInSet",
        name: "MerchantsInSet",
        meta: {
          title: "入驻设置",
          remixIcon: "settings-4-line",
        },
        component: () => import("@/views/Merchants/MerchantsInSet"),
      },
      {
        path: "MerchantsListSet",
        name: "MerchantsListSet",
        meta: {
          title: "商户列表",
          remixIcon: "settings-4-line",
        },
        component: () => import("@/views/Merchants/MerchantsListSet"),
      },
    ],
  },
  {
    path: "/Multistore",
    redirect: "/Multistore/StoreList",
    component: Layout,
    hidden: true,
    meta: {
      title: "多门店",
      remixIcon: "store-2-line",
    },
    children: [
      {
        path: "StoreList",
        name: "StoreList",
        meta: {
          title: "门店管理",
          access: nodes.Multistore_MultistoreList,
          remixIcon: "store-3-line",
        },
        component: () => import("@/views/Multistore/store/StoreList"),
      },
      {
        path: "AddStore",
        name: "AddStore",
        meta: {
          title: "创建店铺",
          access: nodes.MultistoreList_AddStore,
          remixIcon: "store-3-line",
        },
        component: () => import("@/views/SystemSettings/liansuoguanli/AddShop.vue"),
      },
      {
        path: "shopAdministration",
        name: "shopAdministration",
        meta: {
          title: "商品管理",
          access: nodes.MultistoreList_shopGoodsManage,
        },
        component: () => import("@/views/Multistore/store/shopAdministration"),
      },
      {
        path: "StoreShopowner",
        name: "StoreShopowner",
        meta: {
          title: "门店店长",
          access: nodes.MultistoreList_shopManager,
        },
        component: () => import("@/views/Multistore/store/StoreShopowner"),
      },
      // {
      //   path: "lookGoods",
      //   name: "lookGoods",
      //   meta: {
      //     title: "查看商品",
      //   },
      //   component: () => import("@/views/Multistore/store/lookGoods"),
      // },
      {
        path: "GoodsList",
        name: "GoodsList",
        meta: {
          title: "商品管理",
          remixIcon: "shopping-bag-line",
        },
        component: () => import("@/views/Multistore/goods/GoodsList"),
      },
      {
        path: "StoreData",
        name: "StoreData",
        meta: {
          title: "门店数据",
          remixIcon: "computer-line",
        },
        component: () => import("@/views/Multistore/statistics/StoreData"),
      },
      {
        path: "EditStoreData",
        name: "EditStoreData",
        meta: {
          title: "数据详情",
        },
        component: () => import("@/views/Multistore/statistics/EditStoreData"),
      },
      {
        path: "StoreSet",
        name: "StoreSet",
        meta: {
          access: nodes.Multistore_StoreSet,
          title: "功能设置",
          remixIcon: "settings-4-line",
        },
        component: () => import("@/views/Multistore/settings/StoreSet"),
      },
    ],
  },
  {
    path: "/SingleStore",
    redirect: "/SingleStore/goods",
    component: Layout,
    hidden: true,
    meta: {
      title: "单门店",
      remixIcon: "store-2-line",
    },
    children: [
      {
        path: "goods/GoodsAdministration",
        name: "GoodsAdministration",
        meta: {
          title: "商品管理",
          remixIcon: "apps-line",
        },
        component: () => import("@/views/SingleStore/Goods/GoodsAdministration"),
      },
      {
        path: "goods/GoodsData",
        name: "GoodsData",
        meta: {
          title: "商品资料",
          remixIcon: "apps-line",
        },
        component: () => import("@/views/goods/manageG/BaseDataList"),
      },
      // 价格管理
      {
        path: "sale/priceTable",
        name: "StorePriceTable",
        meta: {
          title: "价格管理",
          remixIcon: "bookmark-3-line",
        },
        component: () => import("@/views/goods/sale/PriceTable"),
      },
      {
        path: "goods/priceAdjust",
        name: "priceAdjust",
        meta: {
          title: "商品调价单",
          remixIcon: "bookmark-3-line",
        },
        component: () => import("@/views/goods/sale/AdjustPrice"),
      },
      {
        path: "goods/priceAdjustCustomer",
        name: "priceAdjustCustomer",
        meta: {
          title: "客户调价",
          remixIcon: "bookmark-3-line",
        },
        component: () => import("@/views/goods/sale/CostumeAdjustPrice"),
      },
      {
        path: "goods/priceAdjustCustomerType",
        name: "priceAdjustCustomerType",
        meta: {
          title: "客户类型调价",
          remixIcon: "bookmark-3-line",
        },
        component: () => import("@/views/goods/sale/CustomerTypeAdjustPrice"),
      },
      // 订单
      {
        path: "order/OrderList/all",
        name: "OrderListAll",
        meta: {
          title: "全部订单",
          remixIcon: "list-unordered",
        },
        component: () => import("@/views/SingleStore/order/OrderList"),
      },
      {
        path: "order/OrderList/waitAudit",
        name: "OrderListWaitAudit",
        meta: {
          title: "待审核",
          remixIcon: "list-unordered",
        },
        component: () => import("@/views/SingleStore/order/OrderList"),
      },
      {
        path: "order/OrderList/waitOutStock",
        name: "OrderListWaitOutStock",
        meta: {
          title: "待发货",
          remixIcon: "list-unordered",
        },
        component: () => import("@/views/SingleStore/order/OrderList"),
      },
      {
        path: "order/OrderList/hasOutStock",
        name: "OrderListHasOutStock",
        meta: {
          title: "待收货",
          remixIcon: "list-unordered",
        },
        component: () => import("@/views/SingleStore/order/OrderList"),
      },
      {
        path: "order/OrderList/finish",
        name: "OrderListFinish",
        meta: {
          title: "已完成",
          remixIcon: "list-unordered",
        },
        component: () => import("@/views/SingleStore/order/OrderList"),
      },
      {
        path: "order/OrderList/close",
        name: "OrderListClose",
        meta: {
          title: "已关闭",
          remixIcon: "list-unordered",
        },
        component: () => import("@/views/SingleStore/order/OrderList"),
      },
      {
        path: "order/StoreOrderAdd",
        name: "StoreOrderAdd",
        meta: {
          title: "代客下单",
          remixIcon: "edit-circle-line",
        },
        component: () => import("@/views/order/manageO/OrderAdd"),
      },
      {
        path: "order/ReturnOrder",
        name: "ReturnOrder",
        meta: {
          title: "退货单",
          remixIcon: "logout-circle-line",
        },
        component: () => import("@/views/SingleStore/order/ReturnOrder"),
      },
      //客户
      {
        path: "Customer/StoreCustomer",
        name: "StoreCustomer",
        meta: {
          title: "客户列表",
          remixIcon: "user-3-line",
        },
        component: () => import("@/views/Customer/CustomerAdmin/CustomerList.vue"),
      },
      //设置
      {
        path: "storeSet/StoreDetail",
        name: "StoreDetail",
        meta: {
          title: "门店信息",
          remixIcon: "list-settings-line",
        },
        component: () => import("@/views/SystemSettings/liansuoguanli/AddShop.vue"),
      },
      // 员工管理
      {
        path: "staffSet/StaffList",
        name: "StaffList",
        meta: {
          title: "员工列表",
          remixIcon: "user-5-line",
        },
        component: () => import("@/views/SystemSettings/accountAdmin/Staff.vue"),
      },
      {
        path: "staffSet/RoleList",
        name: "RoleList",
        meta: {
          title: "角色管理",
          remixIcon: "shield-user-line",
        },
        component: () => import("@/views/SystemSettings/accountAdmin/Role.vue"),
      },
      //  库存
      {
        path: "Inventory/InventoryOut",
        name: "InventoryOut",
        meta: {
          title: "出库管理",
          access: nodes.InventoryOut,
          remixIcon: "home-gear-line",
        },
        component: () => import("@/views/stock/OutIn/outgoing"),
      },
      {
        path: "Inventory/InventoryIn",
        name: "InventoryIn",
        meta: {
          title: "入库管理",
          access: nodes.InventoryIn,
          remixIcon: "home-gear-line",
        },
        component: () => import("@/views/stock/OutIn/storage"),
      },

      {
        path: "Inventory/InventoryQuery",
        name: "InventoryQuery",
        meta: {
          title: "库存查询",
          access: nodes.query,
          remixIcon: "home-gear-line",
        },
        component: () => import("@/views/stock/WarehouseAdmin/query"),
      },
      {
        path: "Inventory/InventoryFlowing",
        name: "InventoryFlowing",
        meta: {
          title: "库存流水",
          access: nodes.flowing,
          remixIcon: "home-gear-line",
        },
        component: () => import("@/views/stock/WarehouseAdmin/flowing"),
      },
      {
        path: "Inventory/InventoryBatch",
        name: "InventoryBatch",
        meta: {
          title: "批次流水",
          access: nodes.Batch,
          remixIcon: "home-gear-line",
        },
        component: () => import("@/views/stock/WarehouseAdmin/Batch"),
      },
      {
        path: "Inventory/InventoryShelfLife",
        name: "InventoryShelfLife",
        meta: {
          title: "保质期查询",
          access: nodes.GetBatch,
          remixIcon: "home-gear-line",
        },
        component: () => import("@/views/stock/WarehouseAdmin/ShelfLife"),
      },

      {
        path: "Inventory/InventoryManagement",
        name: "InventoryManagement",
        meta: {
          title: "仓库管理",
          access: nodes.management,
          remixIcon: "home-6-line",
        },
        component: () => import("@/views/stock/WarehouseManagement/Management"),
      },
      {
        path: "Inventory/InventoryCheck",
        name: "InventoryCheck",
        meta: {
          title: "盘点单",
          access: nodes.inventoryInfo,
          remixIcon: "home-6-line",
        },
        component: () => import("@/views/stock/WarehouseManagement/inventoryInfo"),
      },
      {
        path: "Inventory/InventoryAllocation",
        name: "InventoryAllocation",
        meta: {
          title: "调拨单",
          access: nodes.AllocationForm,
          remixIcon: "home-6-line",
        },
        component: () => import("@/views/stock/WarehouseManagement/AllocationForm.vue"),
      },
      // 采购
      {
        path: "purchase/purchaseList",
        name: "purchaseList",
        meta: {
          title: "采购单",
          access: nodes.PurchaseOrder,
          remixIcon: "shopping-cart-line",
        },
        component: () => import("@/views/Purchase/ManageP/PurchaseOrder"),
      },
      {
        path: "purchase/purchaseReturnList",
        name: "purchaseReturnList",
        meta: {
          title: "采购退货单",
          access: nodes.PurchaseReturnOrder,
          remixIcon: "shopping-cart-line",
        },
        component: () => import("@/views/Purchase/ManageP/PurchaseReturnOrder"),
      },
      {
        path: "purchase/purchaseDetailInfo",
        name: "purchaseDetailInfo",
        meta: {
          title: "采购明细",
          access: nodes.PurchaseDetail,
          remixIcon: "shopping-cart-line",
        },
        component: () => import("@/views/Purchase/ManageP/PurchaseDetail"),
      },
      {
        path: "purchase/SupplierList",
        name: "SupplierList",
        meta: {
          title: "供应商管理",
          access: nodes.Supplier,
          remixIcon: "shopping-cart-line",
        },
        component: () => import("@/views/Purchase/ManageP/Supplier"),
      },
    ],
  },
  {
    path: "/settlement",
    redirect: "/settlement/settlementData",
    component: Layout,
    meta: {
      title: "结算",
      remixIcon: "money-cny-circle-line",
    },
    children: [
      {
        path: "SettlementData",
        name: "SettlementData",
        meta: {
          title: "结算概览",
          remixIcon: "mac-line",
        },
        component: () => import("@/viewsMerch/settlement/index.vue"),
      },
      {
        path: "SettlementManage",
        name: "SettlementManage",
        meta: {
          title: "结算管理",
          remixIcon: "align-bottom",
        },
        component: () => import("@/viewsMerch/settlement/SettlementManage.vue"),
      },
      {
        path: "SettlementDetail",
        name: "SettlementDetail",
        meta: {
          title: "结算详情",
          remixIcon: "align-bottom",
        },
        component: () => import("@/viewsMerch/settlement/SettlementDetail.vue"),
      },
    ],
  },
  {
    path: "/Supplier",
    redirect: "/Supplier/supplierSet",
    component: Layout,
    meta: {
      title: "供应商管理端",
      remixIcon: "award-line",
    },
    children: [
      {
        path: "profile",
        name: "SupplierProfile",
        meta: {
          title: "个人信息",
          remixIcon: "user-settings-line",
          access: nodes.supplierManage_supplierSet,
        },
        component: () => import("@/views/Supplier/SupplierProfile"),
      },
      {
        path: "supplierSet",
        name: "supplierSet",
        meta: {
          title: "设置",
          remixIcon: "award-line",
          access: nodes.supplierManage_supplierSet,
        },
        component: () => import("@/views/Supplier/supplierSet"),
      },
      {
        path: "offerSet",
        name: "offerSet",
        meta: {
          title: "报价单管理",
          remixIcon: "bill-line",
          access: nodes.supplierManage_offerSet,
        },
        component: () => import("@/views/Supplier/offerSet"),
      },
      {
        path: "offerSetInfo",
        name: "offerSetInfo",
        meta: {
          title: "报价单管理",
          remixIcon: "mac-line",
          access: nodes.supplierManage_offerSet,
        },
        component: () => import("@/views/Supplier/offerSetInfo"),
      },
      {
        path: "settlement",
        name: "settlement",
        meta: {
          title: "结算管理",
          access: nodes.supplierManage_SupplierSettlement,
          remixIcon: "calendar-todo-line",
        },
        component: () => import("@/views/Supplier/settlement"),
      },
      {
        path: "consignmentPermission",
        name: "consignmentPermission",
        meta: {
          title: "代销基础配置",
          remixIcon: "settings-3-line",
          access: nodes.supplierManage_supplierSet,
        },
        component: () => import("@/views/Supplier/ConsignmentPermission"),
      },
      {
        path: "consignmentManage",
        name: "consignmentManage",
        meta: {
          title: "代销状态管理",
          remixIcon: "dashboard-line",
          access: nodes.supplierManage_supplierSet,
        },
        component: () => import("@/views/Supplier/ConsignmentManage"),
      },
      {
        path: "DepositAccount",
        name: "DepositAccount",
        meta: {
          title: "保证金账户",
          remixIcon: "bank-card-line",
          access: nodes.depositManage_depositAccount,
        },
        component: () => import("@/views/Supplier/DepositAccount"),
      },
      {
        path: "DepositHistory",
        name: "DepositHistory",
        meta: {
          title: "保证金流水",
          remixIcon: "exchange-funds-line",
          access: nodes.depositAccount_depositHistory,
        },
        component: () => import("@/views/Supplier/DepositHistory"),
      },
      {
        path: "DepositOperationHistory",
        name: "DepositOperationHistory",
        meta: {
          title: "保证金操作管理",
          remixIcon: "lock-line",
          access: nodes.depositManage_depositOperation,
        },
        component: () => import("@/views/Supplier/DepositOperationHistory"),
      },
      {
        path: "SettlementCycle",
        name: "SettlementCycle",
        meta: {
          title: "结算周期",
          remixIcon: "time-line",
          access: nodes.supplierManage_settlementCycle,
        },
        component: () => import("@/views/Supplier/SettlementCycle"),
      },
      {
        path: "SettlementOrders",
        name: "SettlementOrders",
        meta: {
          title: "结算单管理",
          remixIcon: "file-list-3-line",
          access: nodes.supplierManage_settlementOrders,
        },
        component: () => import("@/views/Supplier/SettlementOrders"),
      },
      {
        path: "ConsignmentRules",
        name: "ConsignmentRules",
        meta: {
          title: "分账规则",
          remixIcon: "scales-line",
          access: nodes.supplierManage_consignmentRules,
        },
        component: () => import("@/views/Supplier/ConsignmentRules"),
      },
      {
        path: "ConsignmentRuleForm",
        name: "ConsignmentRuleForm",
        meta: {
          title: "分账规则表单",
          remixIcon: "edit-line",
          access: nodes.supplierManage_consignmentRules,
        },
        component: () => import("@/views/Supplier/ConsignmentRuleForm"),
        hidden: true,
      },

      {
        path: "ConsignmentInventory",
        name: "ConsignmentInventory",
        meta: {
          title: "代销库存管理",
          remixIcon: "store-2-line",
          access: nodes.supplierManage_consignmentInventory,
        },
        component: () => import("@/views/Supplier/ConsignmentInventory"),
      },
      {
        path: "InventoryFlowing",
        name: "InventoryFlowing",
        meta: {
          title: "库存变更记录",
          remixIcon: "file-list-2-line",
          access: nodes.supplierManage_consignmentInventoryFlowing,
        },
        component: () => import("@/views/Supplier/InventoryFlowing"),
      },
      {
        path: "SettlementDetails",
        name: "SettlementDetails",
        meta: {
          title: "分账明细查询",
          remixIcon: "file-search-line",
          access: nodes.supplierManage_settlementDetails,
        },
        component: () => import("@/views/Supplier/SettlementDetails"),
      },
      {
        path: "SettlementStatistics",
        name: "SettlementStatistics",
        meta: {
          title: "分账统计报表",
          remixIcon: "pie-chart-line",
          access: nodes.settlementDetails_settlementStatistics,
        },
        component: () => import("@/views/Supplier/SettlementStatistics"),
      },



    ],
  },
  // 添加车销管理路由
  {
    path: "/CarSale",
    component: Layout,
    redirect: "/CarSale/Vehicle/List",
    name: "CarSale",
    meta: {
      title: "车销管理",
      remixIcon: "car-line",
    },
    children: [
      // 车辆信息管理
      {
        path: "Vehicle/List",
        name: "VehicleList",
        component: () => import("@/views/CarSale/Vehicle/List"),
        meta: {
          title: "车辆信息管理",
        },
      },
      {
        path: "Vehicle/Add",
        name: "VehicleAdd",
        component: () => import("@/views/CarSale/Vehicle/Edit"),
        meta: {
          title: "添加车辆",
        },
        hidden: true,
      },
      {
        path: "Vehicle/Edit/:id",
        name: "VehicleEdit",
        component: () => import("@/views/CarSale/Vehicle/Edit"),
        meta: {
          title: "编辑车辆",
        },
        hidden: true,
      },

      // 利润分成层级管理
      {
        path: "ProfitTier/List",
        name: "ProfitTierList",
        component: () => import("@/views/CarSale/ProfitTier/List"),
        meta: {
          title: "利润分成层级管理",
        },
      },
      {
        path: "ProfitTier/Add",
        name: "ProfitTierAdd",
        component: () => import("@/views/CarSale/ProfitTier/Edit"),
        meta: {
          title: "添加利润分成层级",
        },
        hidden: true,
      },
      {
        path: "ProfitTier/Edit/:id",
        name: "ProfitTierEdit",
        component: () => import("@/views/CarSale/ProfitTier/Edit"),
        meta: {
          title: "编辑利润分成层级",
        },
        hidden: true,
      },

      // 收入账户管理
      {
        path: "Revenue/List",
        name: "RevenueList",
        component: () => import("@/views/CarSale/Revenue/List"),
        meta: {
          title: "收入账户管理",
        },
      },
      {
        path: "Revenue/Add",
        name: "RevenueAdd",
        component: () => import("@/views/CarSale/Revenue/Edit"),
        meta: {
          title: "添加收入记录",
        },
        hidden: true,
      },
      {
        path: "Revenue/Edit/:id",
        name: "RevenueEdit",
        component: () => import("@/views/CarSale/Revenue/Edit"),
        meta: {
          title: "编辑收入记录",
        },
        hidden: true,
      },
      {
        path: "Revenue/Detail/:id",
        name: "RevenueDetail",
        component: () => import("@/views/CarSale/Revenue/Detail"),
        meta: {
          title: "收入详情",
        },
        hidden: true,
      },
      {
        path: "Revenue/Statistics",
        name: "RevenueStatistics",
        component: () => import("@/views/CarSale/Revenue/Statistics"),
        meta: {
          title: "销售统计",
        },
      },
      {
        path: "Revenue/SalesmanStatistics",
        name: "SalesmanStatistics",
        component: () => import("@/views/CarSale/Revenue/SalesmanStatistics"),
        meta: {
          title: "销售员业绩",
        },
      },

      // 员工结算相关路由
      {
        path: "Settlement/Config",
        name: "SettlementConfig",
        component: () => import("@/views/CarSale/Settlement/SettlementConfig"),
        meta: {
          title: "员工结算配置",
        },
      },
      {
        path: "Settlement/PayReceiptList",
        name: "PayReceiptList",
        component: () => import("@/views/CarSale/Settlement/PayReceiptList"),
        meta: {
          title: "应付单管理",
        },
      },
    ],
  },
  // 供应商角色端路由
  {
    path: "/SupplierStorage",
    component: Layout,
    redirect: "/SupplierStorage/SupplierStorageOverview",
    alwaysShow: true,
    meta: {
      title: "供应商仓储",
      remixIcon: "store-2-line",
    },
    children: [
      {
        path: "SupplierStorageOverview",
        name: "SupplierStorageOverview",
        component: () => import("@/views/SupplierStorage/SupplierStorageOverview"),
        meta: {
          title: "供应商概览",
          remixIcon: "mac-line",
        },
      },
      {
        path: "SupplierStorageFlow",
        name: "SupplierStorageFlow",
        component: () => import("@/views/SupplierStorage/SupplierStorageFlow"),
        meta: {
          title: "货物流转查询",
          remixIcon: "truck-line",
        },
      },
      {
        path: "SupplierStoragePrice",
        name: "SupplierStoragePrice",
        component: () => import("@/views/SupplierStorage/SupplierStoragePrice"),
        meta: {
          title: "固定价格查询",
          remixIcon: "price-tag-3-line",
        },
      },
      {
        path: "SupplierStorageInventory",
        name: "SupplierStorageInventory",
        component: () => import("@/views/SupplierStorage/SupplierStorageInventory"),
        meta: {
          title: "库存状态查询",
          remixIcon: "store-2-line",
        },
      },
      {
        path: "SupplierStorageSettlement",
        name: "SupplierStorageSettlement",
        component: () => import("@/views/SupplierStorage/SupplierStorageSettlement"),
        meta: {
          title: "结算流水查询",
          remixIcon: "file-list-3-line",
        },
      },
      {
        path: "SettlementDetailList",
        name: "SettlementDetailList",
        component: () => import("@/views/SupplierStorage/SettlementDetailList"),
        meta: {
          title: "分账明细查询",
          remixIcon: "file-list-2-line",
        },
      },
      {
        path: "SettlementDetailInfo",
        name: "SettlementDetailInfo",
        component: () => import("@/views/SupplierStorage/SettlementDetailInfo"),
        meta: {
          title: "分账明细详情",
          remixIcon: "file-text-line",
          hidden: true, // 隐藏在菜单中，只能通过路由跳转访问
        },
      },
      {
        path: "SupplierStorageAccount",
        name: "SupplierStorageAccount",
        component: () => import("@/views/SupplierStorage/SupplierStorageAccount"),
        meta: {
          title: "账户管理",
          remixIcon: "user-settings-line",
        },
      },
    ],
  },
];

const router = new VueRouter({
  base: publicPath,
  mode: routerMode,
  scrollBehavior: () => ({
    y: 0,
  }),
  routes: constantRoutes,
});

const originalPush = VueRouter.prototype.push;
VueRouter.prototype.push = function push(location, onResolve, onReject) {
  if (onResolve || onReject) return originalPush.call(this, location, onResolve, onReject);
  return originalPush.call(this, location).catch((err) => err);
};

export function resetRouter() {
  router.matcher = new VueRouter({
    base: publicPath,
    mode: routerMode,
    scrollBehavior: () => ({
      y: 0,
    }),
    routes: constantRoutes,
  }).matcher;
}

export default router;

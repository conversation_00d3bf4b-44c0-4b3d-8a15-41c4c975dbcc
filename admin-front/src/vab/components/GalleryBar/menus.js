import { checkActionAccess } from "@/access/check";
import * as nodes from "@/access/node";
import store from "@/store";

const menusList = [
  {
    path: "/",
    redirect: "/index",
    meta: {
      title: "概况",
      remixIcon: "mac-line",
      affix: true,
    },
    children: [
      {
        path: "/index",
        name: "Index",
        noKeepAlive: true,
        meta: {
          title: "经营概况",
          remixIcon: "mac-line",
          affix: true,
        },
        fullPath: "/index",
      },
    ],
    fullPath: "/",
  },
  {
    path: "/goods",
    redirect: "/goods/manageG/BaseDataList",
    alwaysShow: true,
    meta: {
      title: "商品",
      remixIcon: "shopping-bag-line",
    },
    children: [
      {
        path: "/goods/manageG",
        name: "ManageG",
        fullPath: "/goods/sale/PublishGoods",
        meta: {
          title: "商品管理",
          remixIcon: "apps-line",
        },
        children: [
          {
            path: "/goods/sale/PublishGoods",
            name: "PublishGoods",
            meta: {
              title: "商品列表",
              access: nodes.PublishGoods,
            },
            fullPath: "/goods/sale/PublishGoods",
          },
          {
            path: "/goods/manageG/BaseDataList",
            name: "BaseDataList",
            meta: {
              title: "商品资料",
              access: nodes.BaseDataList,
            },
            fullPath: "/goods/manageG/BaseDataList",
          },
          {
            path: "/goods/manageG/GoodsClassify",
            name: "GoodsClassify",
            meta: {
              title: "商品分类",
              access: nodes.GoodsClassify,
            },
            fullPath: "/goods/manageG/GoodsClassify",
          },

          {
            path: "/goods/manageG/BrandManage",
            name: "BrandManage",
            meta: {
              title: "商品品牌",
              access: nodes.BrandManage,
            },
            fullPath: "/goods/manageG/BrandManage",
          },
          {
            path: "/goods/manageG/UnitMeasurement",
            name: "UnitMeasurement",
            meta: {
              title: "单位管理",
              access: nodes.UnitSet,
            },
            fullPath: "/goods/manageG/UnitMeasurement",
          },
          {
            path: "/goods/manageG/SpecManage",
            name: "SpecManage",
            meta: {
              title: "属性管理",
              access: nodes.specManage,
            },
            fullPath: "/goods/manageG/SpecManage",
          },
          {
            path: "/goods/goodsemprego/GoodsEmprego",
            name: "GoodsEmprego",
            meta: {
              title: "商品服务",
              access: nodes.GoodsEmprego,
            },
            fullPath: "/goods/goodsemprego/GoodsEmprego",
          },
          {
            path: "/goods/manageG/GoodsGrouping",
            name: "GoodsGrouping",
            meta: {
              title: "商品分组",
              access: nodes.GoodsGrouping,
            },
            fullPath: "/goods/manageG/GoodsGrouping",
          },
        ],
      },
      {
        path: "/goods/sale",
        name: "Sale",
        fullPath: "/goods/sale/PublishGoods",
        meta: {
          title: "商品价格",
          remixIcon: "bookmark-3-line",
        },
        children: [
          {
            path: "/goods/sale/PriceTable",
            name: "PriceTable",
            meta: {
              title: "价格表",
              access: nodes.PriceTable,
            },
            fullPath: "/goods/sale/PriceTable",
          },
          {
            path: "/goods/sale/AdjustPriceGoods",
            name: "AdjustPriceGoods",
            meta: {
              title: "调价单",
              access: nodes.AdjustPrice,
            },
            fullPath: "/goods/sale/AdjustPriceGoods",
          },
        ],
      },
    ],
    fullPath: "/goods",
  },
  {
    path: "/order",
    name: "Order",
    redirect: "/order/manageO/orderQuery",
    alwaysShow: true,
    meta: {
      title: "订单",
      remixIcon: "file-list-2-line",
    },
    children: [
      {
        path: "/order/manageO/orderQuery",
        name: "ManageO",
        fullPath: "/order/manageO/orderQuery",
        meta: {
          title: "订单管理",
          remixIcon: "list-unordered",
        },
        children: [
          /*{
            path: "/order/manageO/newOrderList",
            name: "NewOrderList",
            meta: {
              title: "新订单列表",
              access: nodes.newOrderList,
            },
            fullPath: "/order/manageO/newOrderList",
          },*/

          {
            path: "/order/manageO/orderQuery",
            name: "OrderQuery",
            meta: {
              title: "订货单",
              access: nodes.orderQuery,
            },
            fullPath: "/order/manageO/orderQuery",
          },
          // {
          //   path: "/order/manageO/OrderDetails",
          //   name: "OrderDetails",
          //   meta: {
          //     title: "订单详情",
          //     access: nodes.orderQuery,
          //   },
          //   fullPath: "/order/manageO/OrderDetails",
          // },
          {
            path: "/order/manageO/selfOrder?type=2",
            name: "SelfOrder",
            meta: {
              title: "自提单",
              access: nodes.selfOrder,
            },
            fullPath: "/order/manageO/selfOrder?type=2",
          },
          {
            path: "/order/manageO/ReturnWarehousingOrder",
            name: "ReturnWarehousingOrder",
            meta: {
              title: "退货单",
              access: nodes.ReturnWarehousingOrder,
            },
            fullPath: "/order/manageO/ReturnWarehousingOrder",
          },
          /*{
            path: "/order/manageO/CancelOrder",
            name: "CancelOrder",
            meta: {
              title: "已取消",
              access: nodes.CancelOrder,
            },
            fullPath: "/order/manageO/CancelOrder",
          },*/
          {
            path: "/order/manageO/OrderAdd",
            name: "OrderAdd",
            meta: {
              noKeepAlive: true,
              title: "代客下单",
              access: nodes.OrderAdd,
            },
            fullPath: "/order/manageO/OrderAdd",
          },
        ],
      },
      /* {
        path: "/order/saleO",
        name: "SaleO",
        fullPath: "/order/saleO/AddSaleOrder",
        meta: {
          title: "销售单管理",
          remixIcon: "bill-line",
        },
        children: [
          {
            path: "/order/saleO/AddSaleOrder",
            name: "AddSaleOrder",
            meta: {
              title: "创建销售单",
              access: nodes.addSaleOrder,
            },
            fullPath: "/order/saleO/AddSaleOrder",
          },
          {
            path: "/order/saleO/SaleOrderList",
            name: "SaleOrderList",
            meta: {
              title: "销售单列表",
              access: nodes.saleOrderList,
            },
            fullPath: "/order/saleO/SaleOrderList",
          },
        ],
      },*/
      {
        path: "/order/SaleTotalForm",
        name: "SaleTotalForm",
        fullPath: "/order/SaleTotalForm/GoodsForm",
        meta: {
          title: "销售报表",
          remixIcon: "file-text-line",
        },
        children: [
          {
            path: "/order/SaleTotalForm/GoodsForm",
            name: "GoodsForm",
            meta: {
              title: "商品汇总表",
              access: nodes.OrderStatistics,
            },
            fullPath: "/order/SaleTotalForm/GoodsForm",
          },
          {
            path: "/order/SaleTotalForm/CustomerForm",
            name: "CustomerForm",
            meta: {
              title: "客户汇总表",
              access: nodes.OrderStatistics,
            },
            fullPath: "/order/SaleTotalForm/CustomerForm",
          },
          {
            path: "/order/SaleTotalForm/StaffForm",
            name: "StaffForm",
            meta: {
              title: "人员汇总表",
              access: nodes.OrderStatistics,
            },
            fullPath: "/order/SaleTotalForm/StaffForm",
          },
        ],
      },
    ],
    fullPath: "/order",
  },
  {
    path: "/Customer",
    name: "Customer",
    redirect: "/Customer/CustomerAdmin/CustomerList",
    alwaysShow: true,
    meta: {
      title: "客户",
      remixIcon: "contacts-line",
    },
    children: [
      {
        path: "/Customer/CustomerAdmin",
        name: "CustomerAdmin",
        fullPath: "/Customer/CustomerAdmin/CustomerList",
        meta: {
          title: "客户管理",
          remixIcon: "user-3-line",
        },
        children: [
          {
            path: "/Customer/CustomerAdmin/CustomerList",
            name: "CustomerList",
            meta: {
              title: "客户列表",
              access: nodes.CustomerList,
            },
            fullPath: "/Customer/CustomerAdmin/CustomerList",
          },
          {
            path: "/Customer/CustomerAdmin/CustomerType",
            name: "CustomerType",
            meta: {
              title: "客户类型",
              access: nodes.CustomerType,
            },
            fullPath: "/Customer/CustomerAdmin/CustomerType",
          },
          {
            path: "/Customer/CustomerAdmin/LabelManagement",
            name: "LabelManagement",
            meta: {
              title: "标签管理",
              access: nodes.LabelManagement,
            },
            fullPath: "/Customer/CustomerAdmin/LabelManagement",
          },
          // {
          //   path: "/Customer/CustomerAdmin/CustomerQuery",
          //   name: "CustomerQuery",
          //   meta: {
          //     title: "客户查询",
          //     access: nodes.CustomerQuery,
          //   },
          //   fullPath: "/Customer/CustomerAdmin/CustomerQuery",
          // },
        ],
      },
      {
        path: "/Customer/CustomerCheck",
        name: "CustomerCheck",
        fullPath: "/Customer/CustomerCheck/NotCheck",
        meta: {
          title: "客户审核",
          remixIcon: "shield-user-line",
        },
        children: [
          {
            path: "/Customer/CustomerCheck/NotCheck",
            name: "NotCheck",
            meta: {
              title: "未审核",
              access: nodes.NotCheck,
            },
            fullPath: "/Customer/CustomerCheck/NotCheck",
          },
          {
            path: "/Customer/CustomerCheck/noPerfectData",
            name: "NoPerfectData",
            meta: {
              title: "待完善资料",
              access: nodes.getAuditAllCustomer,
            },
            fullPath: "/Customer/CustomerCheck/noPerfectData",
          },
        ],
      },
      {
        path: "/Customer/CustomerBehavior",
        name: "CustomerBehavior",
        fullPath: "/Customer/CustomerBehavior/BrowsingHistory",
        meta: {
          title: "客户行为",
          remixIcon: "user-shared-line",
        },
        children: [
          {
            path: "/Customer/CustomerBehavior/BrowsingHistory",
            name: "BrowsingHistory",
            meta: {
              title: "浏览记录",
              access: nodes.CustomerBehaviorBrowsingHistory,
            },
            fullPath: "/Customer/CustomerBehavior/BrowsingHistory",
          },
          {
            path: "/Customer/CustomerBehavior/PurchaseHistory",
            name: "PurchaseHistory",
            meta: {
              title: "购买记录",
              access: nodes.CustomerBehaviorPurchaseHistory,
            },
            fullPath: "/Customer/CustomerBehavior/PurchaseHistory",
          },
          {
            path: "/Customer/CustomerBehavior/DemandReporting",
            name: "DemandReporting",
            meta: {
              title: "需求提报",
              access: nodes.CustomerBehaviorDemandReporting,
            },
            fullPath: "/Customer/CustomerBehavior/DemandReporting",
          },
        ],
      },
      {
        path: "/Customer/CustomerStatements",
        name: "CustomerStatements",
        fullPath: "/Customer/CustomerStatements/AnomalyAnalysisTable",
        meta: {
          title: "客户报表",
          remixIcon: "file-list-line",
        },
        children: [
          {
            path: "/Customer/CustomerStatements/AnomalyAnalysisTable",
            name: "AnomalyAnalysisTable",
            meta: {
              title: "异常客户分析表",
              access: nodes.CustomerStatementsAnomalyAnalysisTable,
            },
            fullPath: "/Customer/CustomerStatements/AnomalyAnalysisTable",
          },
          {
            path: "/Customer/CustomerStatements/PullNewStatistics",
            name: "CustomerPullNewStatistics",
            meta: {
              title: "拉新统计",
              access: nodes.CustomerStatementsPullNewStatistics,
            },
            fullPath: "/Customer/CustomerStatements/PullNewStatistics",
          },
          {
            path: "/Customer/CustomerStatements/VisitRepor",
            name: "VisitRepor",
            meta: {
              title: "拜访报表",
              access: nodes.CustomerStatementsVisitRepor,
            },
            fullPath: "/Customer/CustomerStatements/VisitRepor",
          },
          {
            path: "/Customer/CustomerStatements/CustomerMap",
            name: "CustomerMap",
            meta: {
              title: "客户分布图",
              access: nodes.CustomerStatementsCustomerDistribution,
            },
            fullPath: "/Customer/CustomerStatements/CustomerMap",
          },
          // {
          //   path: "/Customer/CustomerStatements/StaffPullNewStatistics",
          //   name: "StaffPullNewStatistics",
          //   meta: {
          //     title: "员工拉新统计",
          //   },
          //   fullPath: "/Customer/CustomerStatements/StaffPullNewStatistics",
          // },
        ],
      },
    ],
    fullPath: "/Customer",
  },
  {
    path: "/Purchase",
    redirect: "/Purchase/ManageP/Supplier",
    alwaysShow: true,
    meta: {
      title: "采购",
      remixIcon: "shopping-cart-line",
    },
    children: [
      {
        path: "/Purchase/ManageP",
        name: "ManageP",
        fullPath: "/Purchase/ManageP/Supplier",
        meta: {
          title: "采购管理",
          remixIcon: "shopping-cart-line",
        },
        children: [
          {
            path: "/Purchase/ManageP/PurchaseOrder",
            name: "PurchaseOrder",
            meta: {
              title: "采购单",
              access: nodes.PurchaseOrder,
            },
            fullPath: "/Purchase/ManageP/PurchaseOrder",
          },
          {
            path: "/Purchase/ManageP/PurchaseReturnOrder",
            name: "PurchaseReturnOrder",
            meta: {
              title: "采购退货单",
              access: nodes.PurchaseReturnOrder,
            },
            fullPath: "/Purchase/ManageP/PurchaseReturnOrder",
          },
          {
            path: "/Purchase/ManageP/Supplier",
            name: "Supplier",
            meta: {
              title: "供应商",
              access: nodes.Supplier,
            },
            fullPath: "/Purchase/ManageP/Supplier",
          },
          {
            path: "/Purchase/ManageP/Stockout",
            name: "Stockout",
            meta: {
              title: "缺货单",
              access: nodes.Stockout,
            },
            fullPath: "/Purchase/ManageP/Stockout",
          },
          {
            path: "/Purchase/ManageP/Merchant",
            name: "Merchant",
            meta: {
              title: "商户采购单",
              access: nodes.Merchant,
            },
            fullPath: "/Purchase/ManageP/Merchant",
          },
          {
            path: "/Purchase/CostPrice/CostPriceList",
            name: "CostPriceList",
            meta: {
              title: "成本价",
              access: nodes.CostPriceGetAllCostPrice,
            },
            fullPath: "/Purchase/CostPrice/CostPriceList",
          },
        ],
      },
      {
        path: "/Purchase/PurchaseTotalForm",
        name: "PurchaseTotalForm",
        fullPath: "/Purchase/PurchaseTotalForm/GoodsForm",
        meta: {
          title: "采购报表",
          access: nodes.PurchaseStatistics,
          remixIcon: "file-text-line",
        },
        children: [
          {
            path: "/Purchase/ManageP/PurchaseDetail",
            name: "PurchaseDetail",
            meta: {
              title: "采购明细表",
              access: nodes.PurchaseDetail,
            },
            fullPath: "/Purchase/ManageP/PurchaseDetail",
          },
          {
            path: "/Purchase/PurchaseTotalForm/GoodsForm",
            name: "PurchaseGoodsForm",
            meta: {
              title: "商品汇总表",
              access: nodes.PurchaseStatisticsGetAllPurchaseByFields,
            },
            fullPath: "/Purchase/PurchaseTotalForm/GoodsForm",
          },
          {
            path: "/Purchase/PurchaseTotalForm/SupplierForm",
            name: "PurchaseSupplierForm",
            meta: {
              title: "供应商汇总表",
              access: nodes.GetSupplierAllPurchaseByFields,
            },
            fullPath: "/Purchase/PurchaseTotalForm/SupplierForm",
          },
          {
            path: "/Purchase/PurchaseTotalForm/StaffForm",
            name: "PurchaseStaffForm",
            meta: {
              title: "采购员汇总表",
              access: nodes.GetStaffAllPurchaseByFields,
            },
            fullPath: "/Purchase/PurchaseTotalForm/StaffForm",
          },
        ],
      },
    ],
    fullPath: "/Purchase",
  },
  {
    path: "/stock",
    redirect: "/stock/OutIn/outgoing",
    alwaysShow: true,
    meta: {
      title: "库存",
      remixIcon: "home-8-line",
    },
    children: [
      {
        path: "/stock/WarehouseAdmin",
        name: "WarehouseAdmin",
        fullPath: "/stock/OutIn/outgoing",
        meta: {
          title: "库存管理",
          remixIcon: "home-gear-line",
        },
        children: [
          {
            path: "/stock/OutIn/outgoing",
            name: "Outgoing",
            meta: {
              title: "出库管理",
              access: nodes.InventoryOut,
            },
            fullPath: "/stock/OutIn/outgoing",
          },
          {
            path: "/stock/OutIn/storage",
            name: "Storage",
            meta: {
              title: "入库管理",
              access: nodes.InventoryIn,
            },
            fullPath: "/stock/OutIn/storage",
          },
          // {
          //   path: "/stock/OutIn/inventoryWarning ",
          //   name: "inventoryWarning",
          //   meta: {
          //     title: "库存预警",
          //   },
          //   fullPath: "/stock/OutIn/inventoryWarning",
          // },
          // {
          //   path: "/stock/WarehouseAdmin/query",
          //   name: "Query",
          //   meta: {
          //     title: "库存查询",
          //     access: nodes.query,
          //   },
          //   fullPath: "/stock/WarehouseAdmin/query",
          // },
          // {
          //   path: "/stock/WarehouseAdmin/flowing",
          //   name: "Flowing",
          //   meta: {
          //     title: "库存流水",
          //     access: nodes.flowing,
          //   },
          //   fullPath: "/stock/WarehouseAdmin/flowing",
          // },
          // {
          //   path: "/stock/WarehouseAdmin/Batch",
          //   name: "Batch",
          //   meta: {
          //     title: "批次流水",
          //     access: nodes.Batch,
          //   },
          //   fullPath: "/stock/WarehouseAdmin/Batch",
          // },
          {
            path: "/stock/WarehouseAdmin/ShelfLife",
            name: "ShelfLife",
            meta: {
              title: "保质期查询",
              access: nodes.GetBatch,
            },
            fullPath: "/stock/WarehouseAdmin/ShelfLife",
          },
        ],
      },
      {
        path: "/stock/WarehouseManagement",
        name: "WarehouseManagement",
        fullPath: "/stock/WarehouseManagement/Management",
        meta: {
          title: "仓库管理",
          remixIcon: "home-6-line",
          ccess: nodes.management,
        },
        children: [
          {
            path: "/stock/WarehouseManagement/inventoryInfo",
            name: "InventoryInfo",
            meta: {
              title: "盘点单",
              access: nodes.inventoryInfo,
            },
            fullPath: "/stock/WarehouseManagement/inventoryInfo",
          },
          {
            path: "/stock/WarehouseManagement/AllocationForm",
            name: "AllocationForm",
            meta: {
              title: "调拨单",
              access: nodes.management,
            },
            fullPath: "/stock/WarehouseManagement/AllocationForm",
          },
          {
            path: "/stock/WarehouseManagement/LossReport",
            name: "LossReport",
            meta: {
              title: "报损单",
              access: nodes.LossReport,
            },
            fullPath: "/stock/WarehouseManagement/LossReport",
          },
          {
            path: "/stock/WarehouseManagement/Management",
            name: "Management",
            meta: {
              title: "仓库管理",
              access: nodes.management,
            },
            fullPath: "/stock/WarehouseManagement/Management",
          },
          {
            path: "/stock/WarehouseManagement/WarehouseArea",
            name: "WarehouseArea",
            meta: {
              title: "库区管理",
              access: nodes.WarehouseArea,
            },
            fullPath: "/stock/WarehouseManagement/WarehouseArea",
          },
          {
            path: "/stock/WarehouseManagement/LocationManagement",
            name: "LocationManagement",
            meta: {
              title: "库位管理",
              access: nodes.LocationManagement,
            },
            fullPath: "/stock/WarehouseManagement/LocationManagement",
          },
          // {
          //   path: "/stock/WarehouseManagement/inventoryInfo",
          //   name: "InventoryInfo",
          //   meta: {
          //     title: "盘点单",
          //     access: nodes.inventoryInfo,
          //   },
          //   fullPath: "/stock/WarehouseManagement/inventoryInfo",
          // },
          // {
          //   path: "/stock/WarehouseManagement/AllocationForm",
          //   name: "AllocationForm",
          //   meta: {
          //     title: "调拨单",
          //     access: nodes.management,
          //   },
          //   fullPath: "/stock/WarehouseManagement/AllocationForm",
          // },
          // {
          //   path: "/stock/WarehouseManagement/LossReport",
          //   name: "LossReport",
          //   meta: {
          //     title: "报损单",
          //     access: nodes.management,
          //   },
          //   fullPath: "/stock/WarehouseManagement/LossReport",
          // },
        ],
      },
      {
        path: "/stock/summary",
        name: "Summary",
        fullPath: "/stock/summary/WarehouseStockSummary",
        meta: {
          title: "汇总表",
          remixIcon: "file-text-line",
        },
        children: [
          {
            path: "/stock/WarehouseAdmin/query",
            name: "Query",
            meta: {
              title: "库存查询",
              access: nodes.query,
            },
            fullPath: "/stock/WarehouseAdmin/query",
          },
          {
            path: "/stock/WarehouseAdmin/warehouseInventory",
            name: "warehouseInventory",
            meta: {
              title: "仓库库存",
              access: nodes.warehouseInventory,
            },
            fullPath: "/stock/WarehouseAdmin/warehouseInventory",
          },
          {
            path: "/stock/summary/WarehouseStockSummary",
            name: "WarehouseStockSummary",
            meta: {
              title: "库存汇总",
              access: nodes.inventoryStatistics,
            },
            fullPath: "/stock/summary/WarehouseStockSummary",
          },
          {
            path: "/stock/WarehouseAdmin/flowing",
            name: "Flowing",
            meta: {
              title: "库存流水",
              access: nodes.flowing,
            },
            fullPath: "/stock/WarehouseAdmin/flowing",
          },
          {
            path: "/stock/WarehouseAdmin/Batch",
            name: "Batch",
            meta: {
              title: "批次流水",
              access: nodes.Batch,
            },
            fullPath: "/stock/WarehouseAdmin/Batch",
          },
        ],
      },
      // {
      //   path: "/stock/OrdersForShipment",
      //   name: "OrdersForShipment",
      //   fullPath: "/stock/OrdersForShipment/PickingCenter",
      //   meta: {
      //     title: "订单发货",
      //     remixIcon: "file-text-line",
      //   },
      //   children: [
      //     {
      //       path: "/stock/OrdersForShipment/PickingCenter",
      //       name: "PickingCenter",
      //       meta: {
      //         title: "拣货中心",
      //         // access: nodes.Batch,
      //       },
      //       fullPath: "/stock/OrdersForShipment/PickingCenter",
      //     },
      //     {
      //       path: "/stock/OrdersForShipment/DistributionCenter",
      //       name: "DistributionCenter",
      //       meta: {
      //         title: "配送中心",
      //         // access: nodes.Batch,
      //       },
      //       fullPath: "/stock/OrdersForShipment/DistributionCenter",
      //     },
      //   ],
      // },
      {
        path: "/stock/supplier",
        name: "StockSupplier",
        fullPath: "/stock/supplier/SupplierInventory",
        meta: {
          title: "供应商",
          remixIcon: "file-text-line",
        },
        children: [
          {
            path: "/stock/supplier/SupplierInventory",
            name: "SupplierInventory",
            meta: {
              title: "供应商库存",
              // access: nodes.SupplierInventory,
            },
            fullPath: "/stock/supplier/SupplierInventory",
          },
          {
            path: "/stock/supplier/SupplierInventoryFlowing",
            name: "SupplierInventoryFlowing",
            meta: {
              title: "库存流水",
            },
            fullPath: "/stock/supplier/SupplierInventoryFlowing",
          },
        ],
      },
    ],
    fullPath: "/stock",
  },
  {
    path: "/Finance",
    redirect: "/Finance/Receivable/ReceivableList",
    meta: {
      title: "财务",
      remixIcon: "money-cny-circle-line",
      access: nodes.shouldReceiveManage,
    },
    children: [
      {
        path: "/Finance/Receivable",
        name: "Receivable",
        fullPath: "/Finance/Receivable/ReceivableList",
        meta: {
          title: "应收管理",
          remixIcon: "align-bottom",
        },
        children: [
          {
            path: "/Finance/Receivable/CustomerBalance",
            name: "CustomerBalance",
            meta: {
              title: "客户往来汇总表",
              access: nodes.CustomerBalanceGetAllCustomerBalance,
            },
            fullPath: "/Finance/Receivable/CustomerBalance",
          },
          {
            path: "/Finance/Receivable/ReceivableList",
            name: "ReceivableList",
            meta: {
              title: "应收单列表",
              access: nodes.ReceivableList,
            },
            fullPath: "/Finance/Receivable/ReceivableList",
          },
          {
            path: "/Finance/Receivable/ReceiptList",
            name: "ReceiptList",
            meta: {
              title: "收款单列表",
              access: nodes.ReceiptListGetAllReceived,
            },
            fullPath: "/Finance/Receivable/ReceiptList",
          },
          {
            path: "/Finance/Receivable/ApplyReceipt",
            name: "ApplyReceipt",
            meta: {
              title: "收款申请单",
              access: nodes.ApplyReceipt,
            },
            fullPath: "/Finance/Receivable/ApplyReceipt",
          },
          {
            path: "/Finance/Receivable/CustomerBalanceDetail",
            name: "CustomerBalanceDetail",
            meta: {
              title: "客户往来明细表",
              access: nodes.CustomerBalanceDetailGetAllCustomerBalanceDetail,
            },
            fullPath: "/Finance/Receivable/CustomerBalanceDetail",
          },
          {
            path: "/Finance/Cashier/SellRefundForm/1",
            name: "RefundForm",
            meta: {
              title: "销售退款单",
              access: nodes.RefundForm,
            },
            fullPath: "/Finance/Cashier/SellRefundForm/1",
          },
        ],
      },
      {
        path: "/Finance/Handle",
        name: "Handle",
        fullPath: "/Finance/Handle/HandleList",
        meta: {
          title: "应付管理",
          remixIcon: "align-top",
        },
        children: [
          {
            path: "/Finance/Handle/SupplierBalance",
            name: "SupplierBalance",
            meta: {
              title: "供应商往来汇总表",
              access: nodes.CustomerBalanceGetAllSupplierBalance,
            },
            fullPath: "/Finance/Handle/SupplierBalance",
          },
          {
            path: "/Finance/Handle/HandleList",
            name: "HandleList",
            meta: {
              title: "应付单列表",
              access: nodes.HandleList,
            },
            fullPath: "/Finance/Handle/HandleList",
          },
          {
            path: "/Finance/Handle/PaymentList",
            name: "PaymentList",
            meta: {
              title: "付款单列表",
              access: nodes.PaymentListGetAllPaid,
            },
            fullPath: "/Finance/Handle/PaymentList",
          },
          {
            path: "/Finance/Handle/SupplierBalanceDetails",
            name: "SupplierBalanceDetails",
            meta: {
              title: "供应商往来明细表",
              access: nodes.SupplierBalanceDetailsGetAllSupplierBalanceDetail,
            },
            fullPath: "/Finance/Handle/SupplierBalanceDetails",
          },
          // {
          //   path: "/Finance/Cashier/PurchaseRefundForm/2",
          //   name: "RefundForm",
          //   meta: {
          //     title: "采购退款单",
          //     access: nodes.RefundForm,
          //   },
          //   fullPath: "/Finance/Cashier/PurchaseRefundForm/2",
          // },
        ],
      },
      {
        path: "/Finance/Cashier",
        name: "Cashier",
        fullPath: "/Finance/Cashier/FunTransfer",
        meta: {
          title: "出纳管理",
          remixIcon: "bank-card-line",
        },
        children: [
          {
            path: "/Finance/Cashier/FunTransfer",
            name: "FunTransfer",
            meta: {
              title: "资金转账单",
              access: nodes.FunTransfer,
            },
            fullPath: "/Finance/Cashier/FunTransfer",
          },
          {
            path: "/Finance/Cashier/AccountList",
            name: "AccountList",
            meta: {
              title: "资金账户管理",
              access: nodes.AccountList,
            },
            fullPath: "/Finance/Cashier/AccountList",
          },
          {
            path: "/Finance/Cashier/AccountDetails",
            name: "AccountDetails",
            meta: {
              title: "账户明细查询",
              access: nodes.AccountDetailsGetAllAccountDetail,
            },
            fullPath: "/Finance/Cashier/AccountDetails",
          },
          {
            path: "/Finance/Cashier/CostSheet",
            name: "CostSheet",
            meta: {
              title: "费用单",
              access: nodes.CostSheet,
            },
            fullPath: "/Finance/Cashier/CostSheet",
          },
          // {
          //   path: "/Finance/Cashier/RefundForm",
          //   name: "RefundForm",
          //   meta: {
          //     title: "退款单",
          //     access: nodes.RefundForm,
          //   },
          //   fullPath: "/Finance/Cashier/RefundForm",
          // },
        ],
      },
      {
        path: "/Finance/ManageF",
        name: "ManageF",
        fullPath: "/Finance/ManageF/FinanceType",
        meta: {
          title: "财务管理",
          remixIcon: "settings-line",
        },
        children: [
          {
            path: "/Finance/ManageF/FinanceType",
            name: "FinanceType",
            meta: {
              title: "财务类型",
              access: nodes.FinanceTypeGetAllFinanceType,
            },
            fullPath: "/Finance/ManageF/FinanceType",
          },
          {
            path: "/Finance/ManageF/WithdrawAsh",
            name: "WithdrawAsh",
            meta: {
              title: "余额提现",
              access: nodes.WithdrawAsh,
            },
            fullPath: "/Finance/ManageF/WithdrawAsh",
          },
          {
            path: "/Finance/Cashier/CostTypes",
            name: "CostTypes",
            meta: {
              title: "费用类型",
              access: nodes.CostSheet,
            },
            fullPath: "/Finance/Cashier/CostTypes",
          },
        ],
      },
      {
        path: "/Finance/Supplier",
        name: "SupplierPayment",
        fullPath: "/Finance/Supplier/SupplierPaymentList",
        meta: {
          title: "供应商管理",
          remixIcon: "bank-card-line",
        },
        children: [
          {
            path: "/Finance/Supplier/SupplierPaymentList",
            name: "SupplierPaymentList",
            meta: {
              title: "供应商付款单列表",
              // access: nodes.SupplierPaymentList,
            },
            fullPath: "/Finance/Supplier/SupplierPaymentList",
          },
        ],
      },
    ],
    fullPath: "/Finance",
  },
  {
    path: "/statement",
    redirect: "/statement/statementList/MerchandiseSalesStatement",
    meta: {
      title: "报表",
      remixIcon: "pie-chart-line",
    },
    children: [
      {
        path: "/statement/statementList",
        name: "statementList",
        fullPath: "/statement/statementList/MerchandiseSalesStatement",
        meta: {
          title: "商品报表",
          remixIcon: "function-line",
        },
        children: [
          {
            path: "/statement/statementList/MerchandiseSalesStatement",
            name: "MerchandiseSalesStatement",
            meta: {
              title: "商品销售报表",
              access: nodes.goodsFormMerchandiseSalesStatement,
            },
            fullPath: "/statement/statementList/MerchandiseSalesStatement",
          },
          {
            path: "/statement/statementList/CustomerMerchandiseReport",
            name: "CustomerMerchandiseReport",
            meta: {
              title: "客户商品报表",
              access: nodes.goodsFormCustomerMerchandiseReport,
            },
            fullPath: "/statement/statementList/CustomerMerchandiseReport",
          },
        ],
      },
      {
        path: "/statement/statementList",
        name: "statementList",
        fullPath: "/statement/statementList/MerchandiseSalesStatement",
        meta: {
          title: "订单报表",
          remixIcon: "file-chart-line",
        },
        children: [
          {
            path: "/statement/statementList/CustomerOrderReport",
            name: "CustomerOrderReport",
            meta: {
              title: "客户订单报表",
              access: nodes.orderFormCustomerOrderReport,
            },
            fullPath: "/statement/statementList/CustomerOrderReport",
          },
          {
            path: "/statement/statementList/OrderDataReport",
            name: "OrderDataReport",
            meta: {
              title: "订单数据报表",
              access: nodes.orderFormOrderDataReport,
            },
            fullPath: "/statement/statementList/OrderDataReport",
          },
          {
            path: "/statement/statementList/RegionalOrderReport",
            name: "RegionalOrderReport",
            meta: {
              title: "地区订单报表",
              access: nodes.orderFormRegionalOrderReport,
            },
            fullPath: "/statement/statementList/RegionalOrderReport",
          },
          {
            path: "/statement/statementList/SaleRanking",
            name: "SaleRanking",
            meta: {
              title: "业务员订单表",
              access: nodes.orderFormSalesOrderReport,
            },
            fullPath: "/statement/statementList/SaleRanking",
          },
        ],
      },
      // {
      //   path: "/statement/statementList",
      //   name: "statementList",
      //   fullPath: "/statement/statementList/MerchandiseSalesStatement",
      //   meta: {
      //     title: "销售排行",
      //     remixIcon: "file-chart-line",
      //   },
      //   children: [
      //     {
      //       path: "/statement/statementList/SaleRanking",
      //       name: "SaleRanking",
      //       meta: {
      //         title: "销售排行",
      //         access: nodes.SalesRanking,
      //         remixIcon: "file-chart-line",
      //       },
      //       fullPath: "/statement/statementList/SaleRanking",
      //     },
      //   ],
      // },
    ],
    fullPath: "/statement",
  },
  {
    path: "/SystemSettings",
    redirect: "/SystemSettings/liansuoguanli/ShopList",
    alwaysShow: true,
    meta: {
      title: "设置",
      remixIcon: "settings-4-line",
    },
    children: [
      {
        path: "/SystemSettings/BaseSet",
        name: "BaseSet",
        meta: {
          title: "基本设置",
          remixIcon: "settings-4-line",
        },
        fullPath: "/SystemSettings/BaseSet",
        children: [
          {
            path: "/SystemSettings/BaseSet",
            name: "BaseSet",
            meta: {
              title: "系统设置",
              access: nodes.mallManage,
            },
            fullPath: "/SystemSettings/BaseSet",
          },
          // {
          //   path: "/SystemSettings/TradeSet",
          //   name: "TradeSet",
          //   hidden: true,
          //   meta: {
          //     title: "交易设置",
          //     access: nodes.mallManagepaySetting,
          //   },
          //   fullPath: "/SystemSettings/TradeSet",
          // },
          // {
          //   path: "/SystemSettings/GoodsSet",
          //   name: "GoodsSet",
          //   hidden: true,
          //   meta: {
          //     title: "商品设置",
          //     access: nodes.mallManageGoodsSet,
          //   },
          //   fullPath: "/SystemSettings/GoodsSet",
          // },
          // {
          //   path: "/SystemSettings/CustomerSet",
          //   name: "GoodsSet",
          //   hidden: true,
          //   meta: {
          //     title: "客户设置",
          //     access: nodes.mallManageCustomerSet,
          //   },
          //   fullPath: "/SystemSettings/CustomerSet",
          // },
          {
            path: "/SystemSettings/jiaoyiset/voiceSet",
            name: "VoiceSet",
            meta: {
              title: "语音设置",
              // remixIcon: "notification-3-line",
              access: nodes.voiceSet,
            },
            fullPath: "/SystemSettings/jiaoyiset/voiceSet",
          },
          {
            path: "/SystemSettings/jiaoyiset/PushNotification",
            name: "PushNotification",
            meta: {
              title: "消息推送",
              access: nodes.PushNotification,
            },
            fullPath: "/SystemSettings/jiaoyiset/PushNotification",
          },
          /*{
            path: "/SystemSettings/jiaoyiset/SMSConfiguration",
            name: "SMSConfiguration",
            meta: {
              title: "短信配置",
              // access: nodes.voiceSet,
            },
            fullPath: "/SystemSettings/jiaoyiset/SMSConfiguration",
          },*/
        ],
      },
      {
        path: "/SystemSettings/StepSet",
        meta: {
          title: "其他设置",
          remixIcon: "settings-line",
        },
        fullPath: "/SystemSettings/BaseSet",
        children: [
          {
            path: "/SystemSettings/StepSet",
            name: "StepSet",
            meta: {
              title: "流程设置",
              // remixIcon: "equalizer-line",
              access: nodes.StepSet,
            },
            fullPath: "/SystemSettings/StepSet",
          },
          {
            path: "/SystemSettings/jiaoyiset/PayList",
            name: "PayList",
            meta: {
              title: "支付设置",
              access: nodes.PayList,
              // remixIcon: "wallet-line",
            },
            fullPath: "/SystemSettings/jiaoyiset/PayList",
          },
          {
            path: "/SystemSettings/jiaoyiset/Delivery",
            name: "Delivery",
            meta: {
              title: "配送设置",
              access: nodes.DeliverySet,
            },
            fullPath: "/SystemSettings/jiaoyiset/Delivery",
          },
          {
            path: "/SystemSettings/jiaoyiset/Driver",
            name: "Driver",
            meta: {
              title: "司机列表",
              access: nodes.Driver,
            },
            fullPath: "/SystemSettings/jiaoyiset/Driver",
          },
          {
            path: "/SystemSettings/jiaoyiset/Line",
            name: "Line",
            meta: {
              title: "线路列表",
              access: nodes.line,
            },
            fullPath: "/SystemSettings/jiaoyiset/Line",
          },
          // {
          //   path: "/SystemSettings/PrinterSet",
          //   name: "PrinterSet",
          //   meta: {
          //     title: "打印机设置",
          //     access: nodes.mallManagePrinterSet,
          //   },
          //   fullPath: "/SystemSettings/PrinterSet",
          // },
        ],
      },
      {
        path: "/SystemSettings/liansuoguanli",
        name: "Liansuoguanli",
        fullPath: "/SystemSettings/liansuoguanli/ShopList",
        meta: {
          title: "连锁管理",
          remixIcon: "list-settings-line",
        },
        children: [
          {
            path: "/SystemSettings/liansuoguanli/ShopList",
            name: "ShopList",
            meta: {
              title: "商铺列表",
              access: nodes.ShopList,
              remixIcon: "list-settings-line",
            },
            fullPath: "/SystemSettings/liansuoguanli/ShopList",
          },
        ],
      },

      // {
      //   path: "/SystemSettings/jiaoyiset/",
      //   name: "Delivery",
      //   fullPath: "/SystemSettings/jiaoyiset/Delivery",
      //   meta: {
      //     title: "配送设置",
      //     remixIcon: "truck-line",
      //   },
      //   children: [
      //     {
      //       path: "/SystemSettings/jiaoyiset/Delivery",
      //       name: "Delivery",
      //       meta: {
      //         title: "配送设置",
      //         access: nodes.DeliverySet,
      //       },
      //       fullPath: "/SystemSettings/jiaoyiset/Delivery",
      //     },
      //     {
      //       path: "/SystemSettings/jiaoyiset/Logistics",
      //       name: "Logistics",
      //       meta: {
      //         title: "物流接口",
      //         access: nodes.Logistics,
      //       },
      //       fullPath: "/SystemSettings/jiaoyiset/Logistics",
      //     },
      //   ],
      // },

      {
        path: "/SystemSettings/accountAdmin",
        name: "AccountAdmin",
        fullPath: "/SystemSettings/accountAdmin/Department",
        meta: {
          title: "组织设置",
          remixIcon: "user-settings-line",
        },
        children: [
          {
            path: "/SystemSettings/accountAdmin/Department",
            name: "Department",
            meta: {
              title: "部门管理",
              access: nodes.Department,
            },
            fullPath: "/SystemSettings/accountAdmin/Department",
          },
          {
            path: "/SystemSettings/accountAdmin/Role",
            name: "Role",
            meta: {
              title: "角色管理",
              access: nodes.Role,
            },
            fullPath: "/SystemSettings/accountAdmin/Role",
          },
          {
            path: "/SystemSettings/accountAdmin/Staff",
            name: "Staff",
            meta: {
              title: "员工管理",
              access: nodes.Staff,
            },
            fullPath: "/SystemSettings/accountAdmin/Staff",
          },
          {
            path: "/SystemSettings/accountAdmin/LoginRecord",
            name: "LoginRecord",
            hidden: true,
            meta: {
              title: "登录日志",
              access: nodes.LoginRecord,
            },
            fullPath: "/SystemSettings/accountAdmin/LoginRecord",
          },
        ],
      },
      {
        path: "/ShoppingMall/AppDesign",
        name: "AppDesign",
        hidden: true,
        fullPath: "/ShoppingMall/AppDesign/PageDesignList",
        meta: {
          title: "店铺装修",
          remixIcon: "brush-3-line",
        },
        children: [
          {
            path: "/ShoppingMall/AppDesign/PageDesignList",
            name: "PageDesignList",
            meta: {
              title: "页面设计",
              access: nodes.PageSet,
            },
            fullPath: "/ShoppingMall/AppDesign/PageDesignList",
          },
          {
            path: "/ShoppingMall/AppDesign/StartUpPage",
            name: "StartUpPage",
            meta: {
              title: "启动页",
              access: nodes.StartPage,
            },
            fullPath: "/ShoppingMall/AppDesign/StartUpPage",
          },
          {
            path: "/ShoppingMall/AppDesign/AppStyle",
            name: "AppStyle",
            meta: {
              title: "风格设置",
              access: nodes.StyleSet,
            },
            fullPath: "/ShoppingMall/AppDesign/AppStyle",
          },
          {
            path: "/ShoppingMall/AppDesign/CateSet",
            name: "CateSet",
            meta: {
              title: "分类模版",
              access: nodes.CategorySet,
            },
            fullPath: "/ShoppingMall/AppDesign/CateSet",
          },
        ],
      },
      {
        path: "/ShoppingMall/mendianset/NoticeLsit",
        name: "NoticeLsit",
        hidden: true,
        meta: {
          title: "公告设置",
          access: nodes.Announcement,
          remixIcon: "notification-3-line",
        },
        fullPath: "/ShoppingMall/mendianset/NoticeLsit",
      },
      // {
      //   path: "/ShoppingMall/mendianset/NewsTemplate",
      //   name: "NewsTemplate",
      //   meta: {
      //     title: "消息管理",
      //     access: nodes.Announcement,
      //     remixIcon: "message-2-line",
      //   },
      //   fullPath: "/ShoppingMall/mendianset/NewsTemplate",
      // },
      {
        path: "/ShoppingMall/WxCode",
        name: "WxCode",
        hidden: true,
        fullPath: "/ShoppingMall/WxCode/WxCodeSet",
        meta: {
          title: "微信小程序",
          remixIcon: "mini-program-line",
        },
        children: [
          {
            path: "/ShoppingMall/WxCode/WxCodeSet",
            name: "WxCodeSet",
            meta: {
              title: "小程序设置",
              access: nodes.WxCodeSet,
            },
            fullPath: "/ShoppingMall/WxCode/WxCodeSet",
          },
          {
            path: "/ShoppingMall/WxCode/TemplateSet",
            name: "TemplateSet",
            meta: {
              title: "小程序发布",
              access: nodes.wxPush,
            },
            fullPath: "/ShoppingMall/WxCode/TemplateSet",
          },
        ],
      },
    ],
    fullPath: "/SystemSettings",
  },
  {
    path: "/ShoppingMall",
    redirect: "/ShoppingMall/mendianset/BaseSet",
    meta: {
      title: "商城",
      remixIcon: "store-2-line",
    },
    children: [
      {
        path: "/ShoppingMall/AppDesign",
        name: "AppDesign",
        fullPath: "/ShoppingMall/AppDesign/PageDesignList",
        meta: {
          title: "店铺装修",
          remixIcon: "brush-3-line",
        },
        children: [
          {
            path: "/ShoppingMall/AppDesign/PageDesignList",
            name: "PageDesignList",
            meta: {
              title: "页面设计",
              access: nodes.PageSet,
            },
            fullPath: "/ShoppingMall/AppDesign/PageDesignList",
          },
          {
            path: "/ShoppingMall/AppDesign/StartUpPage",
            name: "StartUpPage",
            meta: {
              title: "启动页",
              access: nodes.StartPage,
            },
            fullPath: "/ShoppingMall/AppDesign/StartUpPage",
          },
          {
            path: "/ShoppingMall/AppDesign/AppStyle",
            name: "AppStyle",
            meta: {
              title: "风格设置",
              access: nodes.StyleSet,
            },
            fullPath: "/ShoppingMall/AppDesign/AppStyle",
          },
          {
            path: "/ShoppingMall/AppDesign/CateSet",
            name: "CateSet",
            meta: {
              title: "分类模版",
              access: nodes.CategorySet,
            },
            fullPath: "/ShoppingMall/AppDesign/CateSet",
          },
        ],
      },
      {
        path: "/ShoppingMall/mendianset/NoticeLsit",
        name: "NoticeLsit",
        meta: {
          title: "公告设置",
          access: nodes.Announcement,
          remixIcon: "notification-3-line",
        },
        fullPath: "/ShoppingMall/mendianset/NoticeLsit",
      },
      // {
      //   path: "/ShoppingMall/mendianset/NewsTemplate",
      //   name: "NewsTemplate",
      //   meta: {
      //     title: "消息管理",
      //     access: nodes.Announcement,
      //     remixIcon: "message-2-line",
      //   },
      //   fullPath: "/ShoppingMall/mendianset/NewsTemplate",
      // },
      {
        path: "/ShoppingMall/WxCode",
        name: "WxCode",
        fullPath: "/ShoppingMall/WxCode/WxCodeSet",
        meta: {
          title: "微信小程序",
          remixIcon: "mini-program-line",
        },
        children: [
          {
            path: "/ShoppingMall/WxCode/WxCodeSet",
            name: "WxCodeSet",
            meta: {
              title: "小程序设置",
              access: nodes.WxCodeSet,
            },
            fullPath: "/ShoppingMall/WxCode/WxCodeSet",
          },
          {
            path: "/ShoppingMall/WxCode/TemplateSet",
            name: "TemplateSet",
            meta: {
              title: "小程序发布",
              access: nodes.wxPush,
            },
            fullPath: "/ShoppingMall/WxCode/TemplateSet",
          },
        ],
      },
    ],
    fullPath: "/ShoppingMall",
  },
  {
    path: "/Application",
    redirect: "/Application/Application",
    alwaysShow: true,
    meta: {
      title: "应用",
      remixIcon: "app-store-line",
    },
    children: [
      {
        path: "/Application/Application",
        name: "Application",
        meta: {
          title: "应用中心",
          access: nodes.Application,
          remixIcon: "app-store-line",
        },
        fullPath: "/Application/Application",
      },
    ],
    fullPath: "/Application",
  },
  {
    path: "/MoneyGoodsBill",
    alwaysShow: true,
    meta: {
      title: "钱货日清对账",
      access: nodes.getTodayStatistics,
      remixIcon: "app-store-line",
    },
    children: [
      {
        path: "/MoneyGoodsBill/index",
        name: "InventoryStatistics",
        meta: {
          title: "库存日对账",
          remixIcon: "hotel-line",
          access: nodes.getTodayStatistics_statisticsInventoryStatistics,
        },
        fullPath: "/MoneyGoodsBill/index",
      },
      {
        path: "/MoneyGoodsBill/saleBill",
        name: "saleBill",
        meta: {
          title: "销售日对账",
          remixIcon: "numbers-line",
          access: nodes.getTodayStatistics_statisticsGetAllOrderData,
        },
        fullPath: "/MoneyGoodsBill/saleBill",
      },
      {
        path: "/MoneyGoodsBill/FinanceBill",
        name: "FinanceBill",
        meta: {
          title: "财务日对账",
          remixIcon: "stack-line",
          access: nodes.getTodayStatistics_statisticsGetTodayStatistics,
        },
        fullPath: "/MoneyGoodsBill/FinanceBill",
      },
      // {
      //   path: "/MoneyGoodsBill/Profit",
      //   name: "Profit",
      //   meta: {
      //     title: "利润表",
      //     remixIcon: "stack-line",
      //   },
      //   fullPath: "/MoneyGoodsBill/Profit",
      // },
    ],
    fullPath: "/MoneyGoodsBill",
  },
  // 添加车载销售模块
  {
    path: "/CarSale",
    redirect: "/CarSale/Vehicle/List",
    alwaysShow: false,
    meta: {
      title: "车载销售",
      remixIcon: "car-line",
      access: nodes.CarSale,
    },
    children: [
      {
        path: "/CarSale/Vehicle",
        name: "Vehicle",
        fullPath: "/CarSale/Vehicle/List",
        meta: {
          title: "车辆管理",
          remixIcon: "car-washing-line",
          access: nodes.Application_Vehicle,
        },
        children: [
          {
            path: "/CarSale/Vehicle/List",
            name: "VehicleList",
            meta: {
              title: "车辆列表",
              access: nodes.Application_Vehicle,
            },
            fullPath: "/CarSale/Vehicle/List",
          },
        ],
      },
      {
        path: "/CarSale/ProfitTier",
        name: "ProfitTier",
        fullPath: "/CarSale/ProfitTier/List",
        meta: {
          title: "利润分成",
          remixIcon: "percent-line",
          access: nodes.Application_ProfitTier,
        },
        children: [
          {
            path: "/CarSale/ProfitTier/List",
            name: "ProfitTierList",
            meta: {
              title: "分成层级管理",
              access: nodes.Application_ProfitTier,
            },
            fullPath: "/CarSale/ProfitTier/List",
          },
        ],
      },
      {
        path: "/CarSale/Revenue",
        name: "Revenue",
        fullPath: "/CarSale/Revenue/List",
        meta: {
          title: "销售记录",
          remixIcon: "money-cny-box-line",
          access: nodes.Application_Revenue,
        },
        children: [
          {
            path: "/CarSale/Revenue/List",
            name: "RevenueList",
            meta: {
              title: "收入记录",
              access: nodes.Application_Revenue,
            },
            fullPath: "/CarSale/Revenue/List",
          },
          {
            path: "/CarSale/Revenue/Statistics",
            name: "RevenueStatistics",
            meta: {
              title: "销售统计",
              access: nodes.Application_RevenueStatistics,
            },
            fullPath: "/CarSale/Revenue/Statistics",
          },
        ],
      },
      // 添加员工结算相关菜单
      {
        path: "/CarSale/Settlement",
        name: "Settlement",
        fullPath: "/CarSale/Settlement/Config",
        meta: {
          title: "员工结算",
          remixIcon: "money-cny-circle-line",
          access: nodes.Application_Settlement,
        },
        children: [
          {
            path: "/CarSale/Settlement/Config",
            name: "SettlementConfig",
            meta: {
              title: "结算配置",
              access: nodes.Settlement_Config,
            },
            fullPath: "/CarSale/Settlement/Config",
          },
          {
            path: "/CarSale/Settlement/PayReceiptList",
            name: "PayReceiptList",
            meta: {
              title: "应付单管理",
              access: nodes.Settlement_PayReceipt,
            },
            fullPath: "/CarSale/Settlement/PayReceiptList",
          },
        ],
      },
    ],
    fullPath: "/CarSale/Vehicle/List",
  },
  {
    path: "/Marketing",
    fullPath: "/Marketing",
    meta: {
      title: "营销",
      remixIcon: "coupon-3-line",
    },
    children: [
      {
        path: "/Marketing/MarketingList",
        name: "MarketingList",
        fullPath: "/Marketing/MarketingList/Coupon",
        meta: {
          title: "优惠券",
          remixIcon: "coupon-3-line",
        },
        children: [
          {
            path: "/Marketing/MarketingList/Coupon",
            name: "Coupon",
            fullPath: "/Marketing/MarketingList/Coupon",
            meta: {
              title: "优惠券列表",
              access: nodes.CouponList,
            },
          },
          {
            path: "/Marketing/MarketingList/ReleaseRecord",
            name: "ReleaseRecord",
            fullPath: "/Marketing/MarketingList/ReleaseRecord",
            meta: {
              title: "发放记录",
              access: nodes.GrantLog,
            },
          },
        ],
      },
      {
        path: "/Marketing/Promotion",
        name: "Promotion",
        fullPath: "/Marketing/Promotion/PromotionList",
        meta: {
          title: "促销管理",
          remixIcon: "bookmark-3-line",
        },
        children: [
          {
            path: "/Marketing/Promotion/PromotionList",
            name: "PromotionList",
            fullPath: "/Marketing/Promotion/PromotionList",
            meta: {
              title: "商品促销",
              access: nodes.ActivityGoods,
              remixIcon: "bookmark-3-line",
            },
          },
        ],
      },
      {
        path: "/Marketing/vip",
        name: "Vip",
        fullPath: "/Marketing/vip/membershipCard",
        meta: {
          title: "会员卡",
          remixIcon: "vip-crown-2-line",
        },
        children: [
          {
            path: "/Marketing/vip/membershipCard",
            name: "membershipCard",
            fullPath: "/Marketing/vip/membershipCard",
            meta: {
              title: "会员卡管理",
              access: nodes.MembershipCard,
            },
          },
          {
            path: "/Marketing/vip/CollectionRecords",
            name: "PromotionList",
            fullPath: "/Marketing/vip/CollectionRecords",
            meta: {
              title: "领取记录",
              access: nodes.CollectionRecords,
            },
          },
        ],
      },
      {
        path: "/Marketing/SetMeal",
        name: "SetMeal",
        fullPath: "/Marketing/SetMeal/index",
        meta: {
          title: "组合套餐",
          remixIcon: "git-repository-private-line",
        },
        children: [
          {
            path: "/Marketing/SetMeal/index",
            name: "index",
            fullPath: "/Marketing/SetMeal/index",
            meta: {
              title: "组合套餐",
              access: nodes.Application_SetMeal,
              remixIcon: "git-repository-private-line",
            },
          },
        ],
      },
    ],
  },
  {
    path: "/PointsMall",
    fullPath: "/PointsMall",
    meta: {
      title: "积分商城",
      remixIcon: "medal-line",
      // access: nodes.Distribution,
    },
    children: [
      {
        path: "/PointsMall/GoodsManage",
        name: "GoodsManage",
        meta: {
          title: "商品管理",
          remixIcon: "medal-line",
          access: nodes.PointsGoodsManage,
        },
        fullPath: "/PointsMall/GoodsManage",
      },
      {
        path: "/PointsMall/ExchangeRecord",
        name: "ExchangeRecord",
        meta: {
          title: "兑换记录",
          remixIcon: "bill-line",
          access: nodes.ExchangeRecord,
        },
        fullPath: "/PointsMall/ExchangeRecord",
      },
      {
        path: "/PointsMall/PointRule",
        name: "PointRule",
        meta: {
          title: "积分规则",
          remixIcon: "price-tag-2-line",
          access: nodes.PointRule,
        },
        fullPath: "/PointsMall/PointRule",
      },
    ],
  },
  {
    path: "FullGive",
    fullPath: "/FullGive",
    meta: {
      title: "满赠",
      remixIcon: "gift-line",
      access: nodes.FullGive_getAllFullGive,
    },
    children: [
      {
        path: "/FullGive/FullGiveList",
        name: "FullGiveList",
        meta: {
          title: "满赠活动",
          remixIcon: "gift-line",
          access: nodes.FullGive_getAllFullGive,
        },
        fullPath: "/FullGive/FullGiveList",
      },
    ],
  },
  {
    path: "/saleCommission",
    fullPath: "/saleCommission",
    meta: {
      title: "销售提成",
      remixIcon: "money-cny-box-line",
      access: nodes.saleCommission,
    },
    children: [
      {
        path: "/saleCommission/CommissionList",
        name: "CommissionList",
        meta: {
          title: "提成记录",
        },
        fullPath: "/saleCommission/CommissionList",
      },
      {
        path: "/saleCommission/CommissionRule",
        name: "CommissionRule",
        meta: {
          title: "提成规则",
          remixIcon: "article-line",
          access: nodes.CommissionRule,
        },
        fullPath: "/saleCommission/CommissionRule",
      },
      {
        path: "/saleCommission/CommissionStatistic",
        name: "CommissionStatistic",
        meta: {
          title: "提成统计",
          remixIcon: "file-chart-line",
          access: nodes.CommissionStatistic,
        },
        fullPath: "/saleCommission/CommissionStatistic",
      },
    ],
  },
  {
    path: "/Distribution",
    fullPath: "/Distribution",
    meta: {
      title: "分销",
      remixIcon: "funds-box-line",
      access: nodes.Distribution,
    },
    children: [
      {
        path: "/Distribution/DOverview",
        name: "DOverview",
        meta: {
          title: "分销商概览",
          remixIcon: "funds-box-line",
          access: nodes.DOverview,
        },
        fullPath: "/Distribution/DOverview",
      },
      {
        path: "/Distribution/DGoodsList",
        name: "DGoodsList",
        meta: {
          title: "分销商品",
          remixIcon: "shopping-bag-2-line",
          access: nodes.DGoodsList,
        },
        fullPath: "/Distribution/DGoodsList",
      },
      {
        path: "/Distribution/DOrderList",
        name: "DOrderList",
        meta: {
          title: "分销订单",
          access: nodes.DOrderList,
          remixIcon: "file-list-2-line",
        },
        fullPath: "/Distribution/DOrderList",
      },
      {
        path: "/Distribution/DistributionList",
        name: "DistributionList",
        meta: {
          title: "分销商等级",
          remixIcon: "vip-diamond-line",
          access: nodes.DistributionList,
        },
        fullPath: "/Distribution/DistributionList",
      },
      {
        path: "/Distribution/Businessman",
        name: "Businessman",
        meta: {
          title: "分销商管理",
          remixIcon: "user-settings-line",
          access: nodes.Businessman,
        },
        fullPath: "/Distribution/Businessman/NotAudit",
        children: [
          {
            path: "/Distribution/Businessman/NotAudit",
            name: "NotAudit",
            meta: {
              title: "待审核",
              access: nodes.BusinessmangetAllBusinessman,
            },
            fullPath: "/Distribution/Businessman/NotAudit",
          },
          {
            path: "/Distribution/Businessman/Distributor",
            name: "Distributor",
            meta: {
              title: "分销商",
              access: nodes.BusinessmangetAllBusinessman,
            },
            fullPath: "/Distribution/Businessman/Distributor",
          },
        ],
      },
      {
        path: "/Distribution/CashOut",
        name: "CashOutM",
        meta: {
          title: "提现管理",
          remixIcon: "money-cny-circle-line",
          access: nodes.CashOut,
        },
        fullPath: "/Distribution/CashOut/NotAuditCashOut",
        children: [
          {
            path: "/Distribution/CashOut/NotAuditCashOut",
            name: "NotAuditCashOut",
            meta: {
              title: "待审核",
              access: nodes.CashOutgetAll,
            },
            fullPath: "/Distribution/CashOut/NotAuditCashOut",
          },
          {
            path: "/Distribution/CashOut/NotCashOut",
            name: "NotCashOut",
            meta: {
              title: "待打款",
              access: nodes.CashOutgetAll,
            },
            fullPath: "/Distribution/CashOut/NotCashOut",
          },
          {
            path: "/Distribution/CashOut/CashOut",
            name: "CashOut",
            meta: {
              title: "已打款",
              access: nodes.CashOutgetAll,
            },
            fullPath: "/Distribution/CashOut/CashOut",
          },
          {
            path: "/Distribution/CashOut/Invalid",
            name: "Invalid",
            meta: {
              title: "无效",
              access: nodes.CashOutgetAll,
            },
            fullPath: "/Distribution/CashOut/Invalid",
          },
        ],
      },
      {
        path: "/Distribution/DistributionSet",
        name: "DistributionSet",
        meta: {
          title: "分销商设置",
          remixIcon: "user-settings-line",
          access: nodes.DistributionSet,
        },
        fullPath: "/Distribution/DistributionSet/BaseDSet",
        children: [
          {
            path: "/Distribution/DistributionSet/BaseDSet",
            name: "BaseDSet",
            meta: {
              title: "基础设置",
              access: nodes.BaseDSet,
            },
            fullPath: "/Distribution/DistributionSet/BaseDSet",
          },
          {
            path: "/Distribution/DistributionSet/SettlementSet",
            name: "SettlementSet",
            meta: {
              title: "结算设置",
              access: nodes.SettlementSet,
            },
            fullPath: "/Distribution/DistributionSet/SettlementSet",
          },
          {
            path: "/Distribution/DistributionSet/TextSet",
            name: "TextSet",
            meta: {
              title: "文字设置",
              access: nodes.TextSet,
            },
            fullPath: "/Distribution/DistributionSet/TextSet",
          },
        ],
      },
    ],
  },
  {
    path: "/BillTemplate",
    redirect: "/BillTemplate/TemplateList",
    alwaysShow: true,
    meta: {
      title: "单据模版",
      remixIcon: "apps-line",
      access: nodes.TemplateList,
    },
    children: [
      {
        path: "/BillTemplate/TemplateList",
        name: "TemplateList",
        meta: {
          title: "模版列表",
          remixIcon: "apps-line",
          access: nodes.TemplateListgetAll,
        },
        fullPath: "/BillTemplate/TemplateList",
      },
    ],
    fullPath: "/BillTemplate",
  },
  {
    path: "/Supplier",
    redirect: "/Supplier/supplierSet",
    alwaysShow: true,
    meta: {
      title: "供应商管理端",
      remixIcon: "apps-line",
    },
    children: [
      {
        path: "/Supplier/supplierSet",
        name: "supplierSet",
        meta: {
          title: "设置",
          remixIcon: "award-line",
          access: nodes.Application_supplierManage,
        },
        fullPath: "/Supplier/supplierSet",
      },
      {
        path: "/Supplier/offerSet",
        name: "offerSet",
        meta: {
          title: "报价单管理",
          access: nodes.supplierManage_offerSet,
          remixIcon: "bill-line",
        },
        fullPath: "/Supplier/offerSet",
      },
      {
        path: "/Supplier/settlement",
        name: "settlement",
        meta: {
          title: "结算管理",
          access: nodes.supplierManage_SupplierSettlement,
          remixIcon: "calendar-todo-line",
        },
        fullPath: "/Supplier/settlement",
      },
      {
        path: "/Supplier/BasicManage",
        name: "BasicManage",
        fullPath: "/Supplier/consignmentPermission",
        meta: {
          title: "基础管理",
          remixIcon: "settings-line",
        },
        children: [
          {
            path: "/Supplier/consignmentPermission",
            name: "consignmentPermission",
            meta: {
              title: "代销基础配置",
              access: nodes.supplierManage_consignmentManage,
              remixIcon: "settings-3-line",
            },
            fullPath: "/Supplier/consignmentPermission",
          },
          {
            path: "/Supplier/consignmentManage",
            name: "consignmentManage",
            meta: {
              title: "代销状态管理",
              access: nodes.consignmentManage_consignmentStatus,
              remixIcon: "dashboard-line",
            },
            fullPath: "/Supplier/consignmentManage",
          },
          {
            path: "/Supplier/SettlementCycle",
            name: "SettlementCycle",
            meta: {
              title: "结算周期",
              remixIcon: "time-line",
              access: nodes.supplierManage_settlementCycle,
            },
            fullPath: "/Supplier/SettlementCycle",
          },
        ],
      },
      {
        path: "/Supplier/DepositManage",
        name: "DepositManage",
        fullPath: "/Supplier/DepositAccount",
        meta: {
          title: "保证金管理",
          remixIcon: "bank-card-line",
        },
        children: [
          {
            path: "/Supplier/DepositAccount",
            name: "DepositAccount",
            meta: {
              title: "保证金账户",
              remixIcon: "bank-card-line",
              access: nodes.depositManage_depositAccount,
            },
            fullPath: "/Supplier/DepositAccount",
          },
          {
            path: "/Supplier/DepositHistory",
            name: "DepositHistory",
            meta: {
              title: "保证金流水",
              remixIcon: "exchange-funds-line",
              access: nodes.depositAccount_depositHistory,
            },
            fullPath: "/Supplier/DepositHistory",
          },
          {
            path: "/Supplier/DepositOperationHistory",
            name: "DepositOperationHistory",
            meta: {
              title: "保证金操作管理",
              remixIcon: "lock-line",
              access: nodes.depositManage_depositOperation,
            },
            fullPath: "/Supplier/DepositOperationHistory",
          },
        ],
      },
      {
        path: "/Supplier/SettlementManage",
        name: "SettlementManage",
        fullPath: "/Supplier/ConsignmentRules",
        meta: {
          title: "分账管理",
          remixIcon: "scales-line",
        },
        children: [
          {
            path: "/Supplier/ConsignmentRules",
            name: "ConsignmentRules",
            meta: {
              title: "分账规则",
              remixIcon: "scales-line",
              access: nodes.supplierManage_consignmentRules,
            },
            fullPath: "/Supplier/ConsignmentRules",
          },
          {
            path: "/Supplier/SettlementOrders",
            name: "SettlementOrders",
            meta: {
              title: "结算单管理",
              remixIcon: "file-list-3-line",
              access: nodes.supplierManage_settlementOrders,
            },
            fullPath: "/Supplier/SettlementOrders",
          },
          {
            path: "/Supplier/SettlementDetails",
            name: "SettlementDetails",
            meta: {
              title: "分账明细",
              remixIcon: "file-list-line",
              access: nodes.supplierManage_settlementDetails,
            },
            fullPath: "/Supplier/SettlementDetails",
          },
          {
            path: "/Supplier/SettlementStatistics",
            name: "SettlementStatistics",
            meta: {
              title: "分账统计",
              remixIcon: "bar-chart-line",
              access: nodes.settlementDetails_settlementStatistics,
            },
            fullPath: "/Supplier/SettlementStatistics",
          },
        ],
      },
      {
        path: "/Supplier/InventoryManage",
        name: "InventoryManage",
        fullPath: "/Supplier/ConsignmentInventory",
        meta: {
          title: "库存管理",
          remixIcon: "archive-line",
        },
        children: [
          {
            path: "/Supplier/ConsignmentInventory",
            name: "ConsignmentInventory",
            meta: {
              title: "库存管理",
              remixIcon: "archive-line",
              access: nodes.supplierManage_consignmentInventory,
            },
            fullPath: "/Supplier/ConsignmentInventory",
          },
          {
            path: "/Supplier/InventoryFlowing",
            name: "InventoryFlowing",
            meta: {
              title: "库存流水",
              remixIcon: "file-list-2-line",
              access: nodes.supplierManage_consignmentInventoryFlowing,
            },
            fullPath: "/Supplier/InventoryFlowing",
          },

        ],
      },


    ],
    fullPath: "/Supplier",
  },
  {
    path: "/Cashier",
    redirect: "/Cashier/CashierList",
    alwaysShow: true,
    meta: {
      title: "收银台",
      remixIcon: "apps-line",
      access: nodes.Cashier,
    },
    children: [
      {
        path: "/Cashier/CashierList",
        name: "CashierList",
        meta: {
          title: "设置",
          remixIcon: "settings-5-line",
          access: nodes.cashierSet,
        },
        fullPath: "/Cashier/CashierList",
      },
      {
        path: "/Cashier/Cashier",
        name: "Cashier",
        meta: {
          title: "收银员",
          remixIcon: "user-2-line",
          access: nodes.Cashiers,
        },
        fullPath: "/Cashier/Cashier",
      },
      {
        path: "/Cashier/ShoppingGuide",
        name: "ShoppingGuide",
        meta: {
          title: "导购员",
          remixIcon: "user-heart-line",
          access: nodes.ShoppingGuide,
        },
        fullPath: "/Cashier/ShoppingGuide",
      },
      {
        path: "/Cashier/Withdrawal",
        name: "Withdrawal",
        meta: {
          title: "提成明细",
          remixIcon: "file-list-3-line",
          access: nodes.Withdrawal,
        },
        fullPath: "/Cashier/Withdrawal",
      },
      {
        path: "/Cashier/Commission",
        name: "Commission",
        meta: {
          title: "提成统计",
          remixIcon: "exchange-funds-line",
          access: nodes.Commission,
        },
        fullPath: "/Cashier/Commission",
      },
      {
        path: "/Cashier/ShiftOver",
        name: "ShiftOver",
        meta: {
          title: "交班记录",
          remixIcon: "exchange-box-line",
          access: nodes.ShiftOver,
        },
        fullPath: "/Cashier/ShiftOver",
      },
      {
        path: "/order/saleO/CashierOrder",
        name: "CashierOrder",
        meta: {
          title: "收银台订单",
          access: nodes.saleOrderList,
          remixIcon: "file-list-2-line",
        },
        fullPath: "/order/saleO/CashierOrder",
      },
    ],
    fullPath: "/Cashier/CashierList",
  },
  {
    path: "/Merchants",
    redirect: "/Merchants/MerchantsOverview",
    alwaysShow: true,
    meta: {
      title: "多商户",
      remixIcon: "apps-line",
    },
    fullPath: "/Merchants/MerchantsOverview",
    children: [
      {
        path: "/Merchants/MerchantsOverview",
        name: "MerchantsOverview",
        hidden: true,
        meta: {
          title: "商户概览",
          remixIcon: "funds-box-line",
        },
        fullPath: "/Merchants/MerchantsOverview",
      },
      {
        path: "/Merchants/MerchartsList",
        meta: {
          title: "商户管理",
          remixIcon: "store-2-line",
        },
        fullPath: "/Merchants/MerchartsList",
        children: [
          {
            path: "/Merchants/MerchartsList",
            name: "MerchartsList",
            meta: {
              title: "商户列表",
              access: nodes.MerchartsList,
            },
            fullPath: "/Merchants/MerchartsList",
          },
          // {
          //   path: "/Merchants/Merchants",
          //   name: "Merchants",
          //   meta: {
          //     title: "入驻申请",
          //   },
          //   fullPath: "/Merchants/Merchants",
          // },
          /*{
            path: "/Merchants/MerchantsBin",
            name: "MerchantsBin",
            meta: {
              title: "回收站",
            },
            fullPath: "/Merchants/MerchantsBin",
          },*/
        ],
      },
      {
        path: "/Merchants/MerchartsShop",
        meta: {
          title: "商品管理",
          remixIcon: "shopping-bag-line",
        },
        fullPath: "/Merchants/MerchartsShop",
        children: [
          {
            path: "/Merchants/MerchartsShop",
            name: "MerchartsShop",
            meta: {
              title: "商户商品",
              access: nodes.MerchartsGoods,
            },
            fullPath: "/Merchants/MerchartsShop",
          },
          // {
          //   path: "/Merchants/MerchantsExamine",
          //   name: "Merchants",
          //   meta: {
          //     title: "待审核商品",
          //   },
          //   fullPath: "/Merchants/MerchantsExamine",
          // },
        ],
      },
      /* {
        path: "/Merchants/MerchantsOrder",
        meta: {
          title: "订单管理",
          remixIcon: "list-unordered",
        },
        fullPath: "/Merchants/MerchantsOrder",
        children: [
          {
            path: "/Merchants/MerchantsOrder",
            name: "MerchantsOrder",
            meta: {
              title: "商户订单",
              remixIcon: "list-unordered",
            },
            fullPath: "/Merchants/MerchantsOrder",
          },
          /!* {
            path: "/Merchants/SafeguardingOrder",
            name: "SafeguardingOrder",
            meta: {
              title: "维权订单",
            },
            fullPath: "/Merchants/SafeguardingOrder",
          },*!/
        ],
      },*/
      {
        path: "/Merchants/MerchantsSettlement",
        name: "MerchantsSettlement",
        meta: {
          title: "结算管理",
          // remixIcon: "secure-payment-line",
          access: nodes.MerchantsSettlement,
        },
        fullPath: "/Merchants/MerchantsSettlement",
      },
      {
        path: "/Merchants/BasicsSet",
        meta: {
          title: "设置",
          remixIcon: "settings-4-line",
        },
        fullPath: "/Merchants/BasicsSet",
        children: [
          {
            path: "/Merchants/MerchantsBasicsSet",
            name: "MerchantsBasicsSet",
            meta: {
              title: "基础设置",
              access: nodes.MerchantsSettlementMerchantsBasicsSet,
            },
            fullPath: "/Merchants/MerchantsBasicsSet",
          },
          // {
          //   path: "/Merchants/MerchantsInSet",
          //   name: "MerchantsInSet",
          //   meta: {
          //     title: "入驻设置",
          //   },
          //   fullPath: "/Merchants/MerchantsInSet",
          // },
          // {
          //   path: "/Merchants/MerchantsListSet",
          //   name: "MerchantsListSet",
          //   meta: {
          //     title: "商户列表",
          //   },
          //   fullPath: "/Merchants/MerchantsListSet",
          // },
        ],
      },
    ],
  },
  {
    path: "/Multistore",
    redirect: "/Multistore/StoreList",
    alwaysShow: true,
    meta: {
      title: "多门店",
      remixIcon: "apps-line",
    },
    fullPath: "/Multistore/StoreList",
    children: [
      {
        path: "/Multistore/StoreList",
        name: "StoreList",
        meta: {
          title: "门店管理",
          access: nodes.Multistore_MultistoreList,
          remixIcon: "store-3-line",
        },
        fullPath: "/Multistore/StoreList",
      },
      // {
      //   path: "/Multistore/GoodsList",
      //   name: "GoodsList",
      //   meta: {
      //     title: "商品管理",
      //     remixIcon: "shopping-bag-line",
      //   },
      //   fullPath: "/Multistore/GoodsList",
      // },
      // {
      //   path: "/Multistore/StoreData",
      //   name: "StoreData",
      //   meta: {
      //     title: "门店数据",
      //     remixIcon: "computer-line",
      //   },
      //   fullPath: "/Multistore/StoreData",
      // },
      {
        path: "/Multistore/StoreSet",
        name: "StoreSet",
        meta: {
          title: "功能设置",
          access: nodes.Multistore_StoreSet,
          remixIcon: "settings-4-line",
        },
        fullPath: "/Multistore/StoreSet",
      },
    ],
  },
];
export function menus() {
  // return menusList;
  const di = (items) => {
    return items.reduce((container, item) => {
      let { children } = item;
      if (children) {
        children = di(children);
      }
      if (children && !children.length) {
        return container;
      }
      if ((children && children.length) || checkActionAccess(item.meta.access)) {
        const target = children ? { ...item, children } : item;
        container.push(target);
      }

      // 重置菜单父级的fullPath，避免首个进入页面没有权限而导致整个菜单没办法使用
      container = container.map((item) => {
        let handelShow = 5;
        if (item.meta.title === "商品资料") {
          handelShow = parseInt(store.getters["MUser/enterpriseScope"]);
        } else if (item.meta.title === "保质期查询") {
          handelShow = parseInt(store.getters["MUser/shelfLifeSetUp"]);
        } else if (item.meta.title === "商铺列表") {
          // 多店铺隐藏商铺列表，单店铺显示
          handelShow = parseInt(store.getters["MUser/enterpriseScope"]) === 5 ? 4 : 5;
        }

        return {
          ...item,
          handelShow: handelShow,
          fullPath: item.children ? item.children[0].fullPath : item.fullPath,
        };
      });
      return container.filter((item) => item.handelShow !== 4);
    }, []);
  };
  return di(menusList);
}

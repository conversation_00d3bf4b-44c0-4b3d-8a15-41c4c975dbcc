<template>
  <ContainerTit>
    <el-card class="box-card">
      <el-form :inline="true" :model="queryParams" class="demo-form-inline">
        <el-form-item>
          <SelectSupplier
            v-model="queryParams.supplierId"
            placeholder="请选择供应商"
            @clear="clearSupplier"
            @change="handleSupplierChange"
            @keyup.enter.native="handleQuery"
          >
            <el-button slot="append" icon="el-icon-search" @click="handleQuery"></el-button>
          </SelectSupplier>
        </el-form-item>
      </el-form>

      <div v-if="accountInfo" class="account-info">
        <el-row :gutter="20">
          <el-col :span="8">
            <el-card shadow="hover" class="account-card">
              <div slot="header" class="clearfix">
                <span>可用保证金</span>
              </div>
              <div class="card-content">
                <div class="amount">{{ $_common.formatNub(accountInfo.balance) }}</div>
                <div class="label">当前可用保证金</div>
              </div>
            </el-card>
          </el-col>
          <el-col :span="8">
            <el-card shadow="hover" class="account-card">
              <div slot="header" class="clearfix">
                <span>冻结保证金</span>
              </div>
              <div class="card-content">
                <div class="amount">{{ $_common.formatNub(accountInfo.frozenAmount) }}</div>
                <div class="label">当前冻结保证金</div>
              </div>
            </el-card>
          </el-col>
          <el-col :span="8">
            <el-card shadow="hover" class="account-card">
              <div slot="header" class="clearfix">
                <span>总保证金</span>
              </div>
              <div class="card-content">
                <div class="amount">{{ $_common.formatNub(accountInfo.totalAmount) }}</div>
                <div class="label">保证金总额</div>
              </div>
            </el-card>
          </el-col>
        </el-row>

        <el-row :gutter="20" class="mt-20">
          <el-col :span="24">
            <el-card shadow="hover">
              <div slot="header" class="clearfix">
                <span>账户操作</span>
              </div>
              <div class="operation-buttons">
                <el-button type="primary" @click="handleDeposit">充值</el-button>
                <el-button type="warning" @click="handleWithdraw">提取</el-button>
                <el-button type="danger" @click="handleFreeze">冻结</el-button>
                <el-button type="success" @click="handleUnfreeze">解冻</el-button>
              </div>
            </el-card>
          </el-col>
        </el-row>
      </div>
      <div v-else class="empty-data">
        <el-empty description="请选择供应商查询账户信息"></el-empty>
      </div>
    </el-card>

    <!-- 充值对话框 -->
    <el-dialog title="保证金充值" :visible.sync="depositDialogVisible" width="500px">
      <el-form ref="depositForm" :model="depositForm" :rules="depositRules" label-width="100px">
        <el-form-item label="供应商">
          <span>{{ currentSupplierName }}</span>
        </el-form-item>
        <el-form-item label="充值金额" prop="amount">
          <el-input-number v-model="depositForm.amount" :min="0.01" :precision="2" :step="100"></el-input-number>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="depositForm.remark" type="textarea" placeholder="请输入备注信息"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="depositDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="submitDeposit">确 定</el-button>
      </div>
    </el-dialog>

    <!-- 提取对话框 -->
    <el-dialog title="保证金提取" :visible.sync="withdrawDialogVisible" width="500px">
      <el-form ref="withdrawForm" :model="withdrawForm" :rules="withdrawRules" label-width="100px">
        <el-form-item label="供应商">
          <span>{{ currentSupplierName }}</span>
        </el-form-item>
        <el-form-item label="可用保证金">
          <span>{{ $_common.formatNub(accountInfo ? accountInfo.balance : 0) }}</span>
        </el-form-item>
        <el-form-item label="提取金额" prop="amount">
          <el-input-number
            v-model="withdrawForm.amount"
            :min="0.01"
            :max="accountInfo ? accountInfo.balance : 0"
            :precision="2"
            :step="100"
          ></el-input-number>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="withdrawForm.remark" type="textarea" placeholder="请输入备注信息"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="withdrawDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="submitWithdraw">确 定</el-button>
      </div>
    </el-dialog>

    <!-- 冻结对话框 -->
    <el-dialog title="保证金冻结" :visible.sync="freezeDialogVisible" width="500px">
      <el-form ref="freezeForm" :model="freezeForm" :rules="freezeRules" label-width="100px">
        <el-form-item label="供应商">
          <span>{{ currentSupplierName }}</span>
        </el-form-item>
        <el-form-item label="可用保证金">
          <span>{{ $_common.formatNub(accountInfo ? accountInfo.balance : 0) }}</span>
        </el-form-item>
        <el-form-item label="冻结金额" prop="amount">
          <el-input-number
            v-model="freezeForm.amount"
            :min="0.01"
            :max="accountInfo ? accountInfo.balance : 0"
            :precision="2"
            :step="100"
          ></el-input-number>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="freezeForm.remark" type="textarea" placeholder="请输入冻结原因"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="freezeDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="submitFreeze">确 定</el-button>
      </div>
    </el-dialog>

    <!-- 解冻对话框 -->
    <el-dialog title="保证金解冻" :visible.sync="unfreezeDialogVisible" width="500px">
      <el-form ref="unfreezeForm" :model="unfreezeForm" :rules="unfreezeRules" label-width="100px">
        <el-form-item label="供应商">
          <span>{{ currentSupplierName }}</span>
        </el-form-item>
        <el-form-item label="冻结保证金">
          <span>{{ $_common.formatNub(accountInfo ? accountInfo.frozenAmount : 0) }}</span>
        </el-form-item>
        <el-form-item label="解冻金额" prop="amount">
          <el-input-number
            v-model="unfreezeForm.amount"
            :min="0.01"
            :max="accountInfo ? accountInfo.frozenAmount : 0"
            :precision="2"
            :step="100"
          ></el-input-number>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="unfreezeForm.remark" type="textarea" placeholder="请输入解冻原因"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="unfreezeDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="submitUnfreeze">确 定</el-button>
      </div>
    </el-dialog>
  </ContainerTit>
</template>

<script>
import {
  depositFunds,
  freezeFunds,
  getConsignmentSuppliers,
  getDepositAccount,
  unfreezeFunds,
  withdrawFunds,
} from "@/api/SupplierConsignment";
import SelectSupplier from "@/component/common/SelectSupplier";

export default {
  name: "DepositAccount",
  components: {
    SelectSupplier,
  },
  data() {
    return {
      queryParams: {
        supplierId: null,
      },
      accountInfo: null,
      supplierOptions: [],
      supplierLoading: false,
      currentSupplierName: "",

      // 充值对话框
      depositDialogVisible: false,
      depositForm: {
        amount: 0,
        remark: "",
      },
      depositRules: {
        amount: [
          { required: true, message: "请输入充值金额", trigger: "blur" },
          { type: "number", min: 0.01, message: "金额必须大于0", trigger: "blur" },
        ],
        remark: [{ max: 200, message: "备注不能超过200个字符", trigger: "blur" }],
      },

      // 提取对话框
      withdrawDialogVisible: false,
      withdrawForm: {
        amount: 0,
        remark: "",
      },
      withdrawRules: {
        amount: [
          { required: true, message: "请输入提取金额", trigger: "blur" },
          { type: "number", min: 0.01, message: "金额必须大于0", trigger: "blur" },
        ],
        remark: [{ max: 200, message: "备注不能超过200个字符", trigger: "blur" }],
      },

      // 冻结对话框
      freezeDialogVisible: false,
      freezeForm: {
        amount: 0,
        remark: "",
      },
      freezeRules: {
        amount: [
          { required: true, message: "请输入冻结金额", trigger: "blur" },
          { type: "number", min: 0.01, message: "金额必须大于0", trigger: "blur" },
        ],
        remark: [
          { required: true, message: "请输入冻结原因", trigger: "blur" },
          { max: 200, message: "备注不能超过200个字符", trigger: "blur" },
        ],
      },

      // 解冻对话框
      unfreezeDialogVisible: false,
      unfreezeForm: {
        amount: 0,
        remark: "",
      },
      unfreezeRules: {
        amount: [
          { required: true, message: "请输入解冻金额", trigger: "blur" },
          { type: "number", min: 0.01, message: "金额必须大于0", trigger: "blur" },
        ],
        remark: [
          { required: true, message: "请输入解冻原因", trigger: "blur" },
          { max: 200, message: "备注不能超过200个字符", trigger: "blur" },
        ],
      },
    };
  },
  created() {
    // 首次进入页面时请求供应商接口
    this.getSupplierList();
  },
  methods: {
    // 获取供应商列表
    getSupplierList() {
      this.supplierLoading = true;
      getConsignmentSuppliers({
        page: 1,
        pageSize: 20,
      })
        .then((response) => {
          this.supplierOptions = response.data;
          this.supplierLoading = false;
        })
        .catch(() => {
          this.supplierLoading = false;
        });
    },

    // 清除供应商选择
    clearSupplier() {
      this.queryParams.supplierId = null;
      this.accountInfo = null;
      this.currentSupplierName = "";
    },

    // 查询按钮
    handleQuery() {
      if (this.queryParams.supplierId) {
        this.getData();
      }
    },

    // 供应商选择变更
    handleSupplierChange(supplierId, supplierRow) {
      if (supplierId && supplierRow && supplierRow.length > 0) {
        this.currentSupplierName = supplierRow[0].title;
        this.getData();
      } else {
        this.accountInfo = null;
        this.currentSupplierName = "";
      }
    },

    // 获取账户数据
    getData() {
      if (!this.queryParams.supplierId) {
        this.$message.warning("请选择供应商");
        return;
      }

      getDepositAccount(this.queryParams.supplierId)
        .then((response) => {
          // 映射接口返回的数据字段到页面期望的字段
          if (response.data) {
            this.accountInfo = {
              balance: response.data.availableDeposit || 0, // 可用余额
              frozenAmount: response.data.frozenDeposit || 0, // 冻结金额
              totalAmount: response.data.depositAccount || 0, // 总金额
              // 保留原始数据以备后用
              originalData: response.data,
            };
          } else {
            this.accountInfo = null;
          }
        })
        .catch(() => {
          this.accountInfo = null;
        });
    },

    // 重置查询
    resetQuery() {
      this.queryParams.supplierId = null;
      this.accountInfo = null;
      this.currentSupplierName = "";
    },

    // 处理充值
    handleDeposit() {
      if (!this.queryParams.supplierId) {
        this.$message.warning("请先选择供应商");
        return;
      }

      this.depositForm = {
        amount: 0,
        remark: "",
      };
      this.depositDialogVisible = true;
    },

    // 提交充值
    submitDeposit() {
      this.$refs.depositForm.validate((valid) => {
        if (valid) {
          depositFunds(this.queryParams.supplierId, this.depositForm.amount, this.depositForm.remark).then(() => {
            this.$message.success("充值成功");
            this.depositDialogVisible = false;
            this.getData();
          });
        }
      });
    },

    // 处理提取
    handleWithdraw() {
      if (!this.queryParams.supplierId) {
        this.$message.warning("请先选择供应商");
        return;
      }

      if (!this.accountInfo || this.accountInfo.balance <= 0) {
        this.$message.warning("可用保证金不足");
        return;
      }

      this.withdrawForm = {
        amount: 0,
        remark: "",
      };
      this.withdrawDialogVisible = true;
    },

    // 提交提取
    submitWithdraw() {
      this.$refs.withdrawForm.validate((valid) => {
        if (valid) {
          withdrawFunds(this.queryParams.supplierId, this.withdrawForm.amount, this.withdrawForm.remark).then(() => {
            this.$message.success("提取成功");
            this.withdrawDialogVisible = false;
            this.getData();
          });
        }
      });
    },

    // 处理冻结
    handleFreeze() {
      if (!this.queryParams.supplierId) {
        this.$message.warning("请先选择供应商");
        return;
      }

      if (!this.accountInfo || this.accountInfo.balance <= 0) {
        this.$message.warning("可用保证金不足");
        return;
      }

      this.freezeForm = {
        amount: 0,
        remark: "",
      };
      this.freezeDialogVisible = true;
    },

    // 提交冻结
    submitFreeze() {
      this.$refs.freezeForm.validate((valid) => {
        if (valid) {
          freezeFunds(this.queryParams.supplierId, this.freezeForm.amount, this.freezeForm.remark).then(() => {
            this.$message.success("冻结成功");
            this.freezeDialogVisible = false;
            this.getData();
          });
        }
      });
    },

    // 处理解冻
    handleUnfreeze() {
      if (!this.queryParams.supplierId) {
        this.$message.warning("请先选择供应商");
        return;
      }

      if (!this.accountInfo || this.accountInfo.frozenAmount <= 0) {
        this.$message.warning("没有冻结保证金");
        return;
      }

      this.unfreezeForm = {
        amount: 0,
        remark: "",
      };
      this.unfreezeDialogVisible = true;
    },

    // 提交解冻
    submitUnfreeze() {
      this.$refs.unfreezeForm.validate((valid) => {
        if (valid) {
          unfreezeFunds(this.queryParams.supplierId, this.unfreezeForm.amount, this.unfreezeForm.remark).then(() => {
            this.$message.success("解冻成功");
            this.unfreezeDialogVisible = false;
            this.getData();
          });
        }
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.account-info {
  margin-top: 20px;
}

.account-card {
  .card-content {
    text-align: center;
    padding: 20px 0;

    .amount {
      font-size: 24px;
      font-weight: bold;
      color: #409eff;
      margin-bottom: 10px;
    }

    .label {
      font-size: 14px;
      color: #606266;
    }
  }
}

.mt-20 {
  margin-top: 20px;
}

.operation-buttons {
  display: flex;
  justify-content: space-around;
  padding: 10px 0;
}

.empty-data {
  padding: 40px 0;
}
</style>

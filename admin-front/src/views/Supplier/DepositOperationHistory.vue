<template>
  <ContainerQuery>
    <div slot="left">
      <el-button type="danger" @click="handleFreeze">冻结保证金</el-button>
      <el-button type="success" @click="handleUnfreeze">解冻保证金</el-button>
      <el-button type="success" @click="handleExport">导出</el-button>
    </div>
    <el-form slot="more" :inline="true" size="small">
      <el-form-item>
        <el-input
          v-model="queryParams.operator"
          clearable
          style="width: 320px"
          placeholder="请输入操作人"
          @keyup.enter.native="pageChange(1)"
          @clear="pageChange(1)"
        >
          <el-button slot="append" icon="el-icon-search" @click="pageChange(1)"></el-button>
        </el-input>
      </el-form-item>
      <el-form-item>
        <SelectSupplier
          v-model="queryParams.supplierId"
          style="width: 220px"
          @clear="clearSupplier"
          @change="handleSupplierChange"
        />
      </el-form-item>
      <el-form-item>
        <el-select
          v-model="queryParams.operationType"
          placeholder="请选择操作类型"
          clearable
          multiple
          collapse-tags
          style="width: 220px"
          @change="pageChange(1)"
        >
          <el-option label="充值" :value="1"></el-option>
          <el-option label="提取" :value="2"></el-option>
          <el-option label="冻结" :value="3"></el-option>
          <el-option label="解冻" :value="4"></el-option>
          <el-option label="结算扣款" :value="5"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-date-picker
          v-model="dateRange"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          value-format="timestamp"
          style="width: 220px"
          @change="handleDateRangeChange"
        >
        </el-date-picker>
      </el-form-item>
    </el-form>
    <el-table
      v-loading="loading"
      :data="historyList"
      style="width: 100%"
      :empty-text="queryParams.supplierId ? '暂无数据' : '请选择供应商'"
    >
      <el-table-column type="index" label="序号" width="60" align="center"></el-table-column>
      <el-table-column prop="no" label="流水号" width="120" align="center" show-overflow-tooltip></el-table-column>
      <el-table-column prop="operationType" label="类型" width="100" align="center">
        <template slot-scope="scope">
          <el-tag :type="getTypeTag(scope.row.operationType)" size="mini">
            {{ getTypeText(scope.row.operationType) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="amount" label="金额" width="120" align="right">
        <template slot-scope="scope">
          <span
            :class="{
              'text-red': [2, 3].includes(scope.row.operationType),
              'text-green': [1, 4].includes(scope.row.operationType),
            }"
          >
            {{ $_common.formatNub(scope.row.amount) }}
          </span>
        </template>
      </el-table-column>
      <el-table-column prop="balanceBefore" label="变动前余额" width="120" align="right">
        <template slot-scope="scope">
          {{ $_common.formatNub(scope.row.balanceBefore) }}
        </template>
      </el-table-column>
      <el-table-column prop="balanceAfter" label="变动后余额" width="120" align="right">
        <template slot-scope="scope">
          {{ $_common.formatNub(scope.row.balanceAfter) }}
        </template>
      </el-table-column>
      <el-table-column prop="createTime" label="操作时间" width="160" align="center">
        <template slot-scope="scope">
          {{ scope.row.createTime ? $_common.formatDate(scope.row.createTime) : "-" }}
        </template>
      </el-table-column>
      <el-table-column prop="operator" label="操作人" width="120" show-overflow-tooltip></el-table-column>
      <el-table-column prop="remark" label="备注" min-width="200" show-overflow-tooltip></el-table-column>
    </el-table>
    <FooterPage
      :page-size="queryParams.limit"
      :total-page.sync="total"
      :current-page.sync="queryParams.page"
      @pageChange="pageChange"
      @sizeChange="sizeChange"
    />

    <!-- 冻结对话框 -->
    <el-dialog title="保证金冻结" :visible.sync="freezeDialogVisible" width="500px">
      <el-form ref="freezeForm" :model="freezeForm" :rules="freezeRules" label-width="100px">
        <el-form-item label="供应商" prop="supplierId">
          <SelectSupplier v-model="freezeForm.supplierId" @change="handleSupplierSelect" />
        </el-form-item>
        <el-form-item v-if="selectedSupplier" label="可用余额">
          <span>{{ $_common.formatNub(selectedSupplier.balance) }}</span>
        </el-form-item>
        <el-form-item label="冻结金额" prop="amount">
          <el-input-number
            v-model="freezeForm.amount"
            :min="0.01"
            :max="selectedSupplier ? selectedSupplier.balance : 0"
            :precision="2"
            :step="100"
            :disabled="!selectedSupplier"
          ></el-input-number>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="freezeForm.remark" type="textarea" placeholder="请输入冻结原因"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="freezeDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="submitFreeze">确 定</el-button>
      </div>
    </el-dialog>

    <!-- 解冻对话框 -->
    <el-dialog title="保证金解冻" :visible.sync="unfreezeDialogVisible" width="500px">
      <el-form ref="unfreezeForm" :model="unfreezeForm" :rules="unfreezeRules" label-width="100px">
        <el-form-item label="供应商" prop="supplierId">
          <SelectSupplier v-model="unfreezeForm.supplierId" @change="handleSupplierSelect" />
        </el-form-item>
        <el-form-item v-if="selectedSupplier" label="冻结金额">
          <span>{{ $_common.formatNub(selectedSupplier.frozenAmount) }}</span>
        </el-form-item>
        <el-form-item label="解冻金额" prop="amount">
          <el-input-number
            v-model="unfreezeForm.amount"
            :min="0.01"
            :max="selectedSupplier ? selectedSupplier.frozenAmount : 0"
            :precision="2"
            :step="100"
            :disabled="!selectedSupplier"
          ></el-input-number>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="unfreezeForm.remark" type="textarea" placeholder="请输入解冻原因"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="unfreezeDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="submitUnfreeze">确 定</el-button>
      </div>
    </el-dialog>
  </ContainerQuery>
</template>

<script>
import {
    exportDepositHistory,
    freezeFunds,
    getDepositAccount,
    getDepositOperationHistory,
    unfreezeFunds,
} from "@/api/SupplierConsignment";
import FooterPage from "@/component/common/FooterPage";
import SelectSupplier from "@/component/common/SelectSupplier";

export default {
  name: "DepositOperationHistory",
  components: {
    FooterPage,
    SelectSupplier,
  },

  data() {
    return {
      // 遮罩层
      loading: false,
      // 总条数
      total: 0,
      // 历史记录列表
      historyList: [],
      // 查询参数
      queryParams: {
        page: 1,
        limit: 10,
        supplierId: null,
        operationType: [],
        operator: "",
        startDate: "",
        endDate: "",
      },
      // 日期范围
      dateRange: [],
      // 当前选中的供应商名称
      currentSupplierName: "",
      // 选中的供应商账户信息
      selectedSupplier: null,

      // 冻结对话框
      freezeDialogVisible: false,
      freezeForm: {
        supplierId: null,
        amount: 0,
        remark: "",
      },
      freezeRules: {
        supplierId: [{ required: true, message: "请选择供应商", trigger: "change" }],
        amount: [
          { required: true, message: "请输入冻结金额", trigger: "blur" },
          { type: "number", min: 0.01, message: "金额必须大于0", trigger: "blur" },
        ],
        remark: [
          { required: true, message: "请输入冻结原因", trigger: "blur" },
          { max: 200, message: "备注不能超过200个字符", trigger: "blur" },
        ],
      },

      // 解冻对话框
      unfreezeDialogVisible: false,
      unfreezeForm: {
        supplierId: null,
        amount: 0,
        remark: "",
      },
      unfreezeRules: {
        supplierId: [{ required: true, message: "请选择供应商", trigger: "change" }],
        amount: [
          { required: true, message: "请输入解冻金额", trigger: "blur" },
          { type: "number", min: 0.01, message: "金额必须大于0", trigger: "blur" },
        ],
        remark: [
          { required: true, message: "请输入解冻原因", trigger: "blur" },
          { max: 200, message: "备注不能超过200个字符", trigger: "blur" },
        ],
      },
    };
  },
  created() {
    this.getData();
  },
  methods: {
    // 获取历史记录列表
    getData() {
      if (this.queryParams.supplierId == null) {
        return;
      }
      this.loading = true;

      // 创建一个新的查询参数对象，将operationType转换为后端期望的参数格式
      const params = { ...this.queryParams };
      if (params.operationType && params.operationType.length > 0) {
        params.operationTypes = params.operationType;
      }
      delete params.operationType;

      getDepositOperationHistory(params)
        .then((response) => {
          this.historyList = response.data.list;
          this.total = response.data.total;
          this.loading = false;
        })
        .catch(() => {
          this.loading = false;
        });
    },

    // 切页
    pageChange(val) {
      this.queryParams.page = val;
      this.getData();
    },

    // 每页数据大小改变
    sizeChange(val) {
      this.queryParams.limit = val;
      this.pageChange(1);
    },

    // 清除供应商选择
    clearSupplier() {
      this.queryParams.supplierId = "";
      this.currentSupplierName = "";
      this.pageChange(1);
    },

    // 供应商选择变更
    handleSupplierChange(supplierId, supplierRow) {
      if (supplierId && supplierRow && supplierRow.length > 0) {
        this.currentSupplierName = supplierRow[0].title || "";
      } else {
        this.currentSupplierName = "";
      }
      this.pageChange(1);
    },

    // 处理日期范围变化
    handleDateRangeChange(val) {
      if (val && val.length === 2) {
        this.queryParams.startDate = parseInt(val[0] / 1000);
        this.queryParams.endDate = parseInt(val[1] / 1000) + 86399;
      } else {
        this.queryParams.startDate = "";
        this.queryParams.endDate = "";
      }
      this.pageChange(1);
    },

    // 导出
    handleExport() {
      this.$confirm("是否确认导出所有保证金操作历史数据?", "警告", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          this.$message.success("正在导出保证金操作历史数据，请稍候...");

          // 创建一个新的查询参数对象，将operationType转换为后端期望的参数格式
          const params = { ...this.queryParams };
          if (params.operationType && params.operationType.length > 0) {
            params.operationTypes = params.operationType;
          }
          delete params.operationType;

          return exportDepositHistory(params);
        })
        .then((response) => {
          this.downloadFile(response, `保证金操作历史_${new Date().getTime()}.xlsx`);
        });
    },

    // 下载文件
    downloadFile(res, fileName) {
      const blob = new Blob([res], { type: "application/vnd.ms-excel" });
      const link = document.createElement("a");
      link.href = URL.createObjectURL(blob);
      link.download = fileName;
      link.click();
      URL.revokeObjectURL(link.href);
    },



    // 处理冻结操作
    handleFreeze() {
      this.freezeForm = {
        supplierId: null,
        amount: 0,
        remark: "",
      };
      this.selectedSupplier = null;
      this.freezeDialogVisible = true;
    },

    // 处理解冻操作
    handleUnfreeze() {
      this.unfreezeForm = {
        supplierId: null,
        amount: 0,
        remark: "",
      };
      this.selectedSupplier = null;
      this.unfreezeDialogVisible = true;
    },

    // 处理供应商选择
    handleSupplierSelect(supplierId, supplierRow) {
      if (supplierId) {
        if (supplierRow && supplierRow.length > 0) {
          this.currentSupplierName = supplierRow[0].title || "";
        }
        getDepositAccount(supplierId).then((response) => {
          this.selectedSupplier = response.data;
        });
      } else {
        this.selectedSupplier = null;
      }
    },

    // 提交冻结
    submitFreeze() {
      this.$refs.freezeForm.validate((valid) => {
        if (valid) {
          freezeFunds(this.freezeForm.supplierId, this.freezeForm.amount, this.freezeForm.remark).then(() => {
            this.$message.success("冻结成功");
            this.freezeDialogVisible = false;
            this.getData();
          });
        }
      });
    },

    // 提交解冻
    submitUnfreeze() {
      this.$refs.unfreezeForm.validate((valid) => {
        if (valid) {
          unfreezeFunds(this.unfreezeForm.supplierId, this.unfreezeForm.amount, this.unfreezeForm.remark).then(() => {
            this.$message.success("解冻成功");
            this.unfreezeDialogVisible = false;
            this.getData();
          });
        }
      });
    },

    // 获取类型标签样式
    getTypeTag(type) {
      const numTypeMap = {
        1: "success", // 充值
        2: "warning", // 扣除
        3: "danger", // 冻结
        4: "info", // 解冻
        5: "primary", // 结算扣款
      };
      return numTypeMap[type] || "";
    },

    // 获取类型文本
    getTypeText(type) {
      // 处理数字类型的 operationType
      const numTypeMap = {
        1: "充值",
        2: "扣除",
        3: "冻结",
        4: "解冻",
        5: "结算扣款",
      };
      return numTypeMap[type] || `未知类型(${type})`;
    },




  },
};
</script>

<style lang="scss" scoped>
.operation-buttons {
  margin: 20px 0;
  display: flex;
  gap: 10px;
}

.text-red {
  color: #f56c6c;
}

.text-green {
  color: #67c23a;
}
</style>

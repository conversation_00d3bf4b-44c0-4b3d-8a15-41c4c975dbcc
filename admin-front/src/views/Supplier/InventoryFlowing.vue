<template>
  <ContainerQuery>
    <el-form slot="more" :inline="true" size="small">
      <el-form-item>
        <el-input
          v-model="queryParams.keyword"
          clearable
          style="width: 320px"
          placeholder="请输入商品名称/编码"
          @keyup.enter.native="pageChange(1)"
          @clear="pageChange(1)"
        >
          <el-button slot="append" icon="el-icon-search" @click="pageChange(1)"></el-button>
        </el-input>
      </el-form-item>
      <el-form-item>
        <SelectSupplier
          v-model="queryParams.supplierId"
          style="width: 220px"
          @clear="clearSupplier"
          @change="handleSupplierChange"
        />
      </el-form-item>
      <el-form-item>
        <el-select
          v-model="queryParams.actionType"
          placeholder="请选择操作类型"
          clearable
          style="width: 220px"
          @change="pageChange(1)"
        >
          <el-option label="全部" value=""></el-option>
          <el-option label="入库" :value="5"></el-option>
          <el-option label="出库" :value="4"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-select
          v-model="queryParams.source"
          placeholder="请选择来源类型"
          clearable
          style="width: 220px"
          @change="pageChange(1)"
        >
          <el-option label="全部" value=""></el-option>
          <el-option label="销售订单" :value="1"></el-option>
          <el-option label="采购订单" :value="2"></el-option>
          <el-option label="销售退货" :value="6"></el-option>
          <el-option label="调拨库存" :value="7"></el-option>
          <el-option label="调拨出库" :value="8"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-date-picker
          v-model="dateRange"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          value-format="timestamp"
          @change="handleDateRangeChange"
        >
        </el-date-picker>
      </el-form-item>
    </el-form>
    <el-table v-loading="loading" :data="recordList" style="width: 100%" :border="true">
      <el-table-column prop="id" label="流水ID" width="80" align="center"></el-table-column>
      <el-table-column prop="materielCode" label="商品编码" width="120" show-overflow-tooltip></el-table-column>
      <el-table-column prop="materielName" label="商品名称" min-width="180" show-overflow-tooltip>
        <template slot-scope="scope">
          <div class="product-info">
            <el-image
              v-if="scope.row.images && scope.row.images.length > 0"
              :src="scope.row.images[0]"
              :preview-src-list="scope.row.images"
              class="product-image"
            ></el-image>
            <div class="product-text">
              <div class="product-name">{{ scope.row.materielName }}</div>
              <div v-if="scope.row.skuName" class="sku-name">{{ scope.row.skuName }}</div>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="actionType" label="操作类型" width="100" align="center">
        <template slot-scope="scope">
          <el-tag :type="getActionTypeTag(scope.row.actionType)">
            {{ getActionTypeText(scope.row.actionType) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="inventoryNum" label="变动数量" width="120" align="center">
        <template slot-scope="scope">
          <span :class="{ 'text-red': scope.row.actionType === 4, 'text-green': scope.row.actionType === 5 }">
            {{ scope.row.actionType === 4 ? "-" : "+" }}{{ $_common.formatNub(Math.abs(scope.row.inventoryNum)) }}
            {{ scope.row.unitName }}
          </span>
        </template>
      </el-table-column>
      <el-table-column prop="inventoryChangeNum" label="变动后库存" width="120" align="center">
        <template slot-scope="scope">
          {{ $_common.formatNub(scope.row.inventoryChangeNum) }} {{ scope.row.unitName }}
        </template>
      </el-table-column>
      <el-table-column prop="costPrice" label="成本价" width="100" align="right">
        <template slot-scope="scope">
          {{ $_common.formatNub(scope.row.costPrice || scope.row.averageCost) }}
        </template>
      </el-table-column>
      <el-table-column prop="source" label="来源类型" width="100" align="center">
        <template slot-scope="scope">
          <el-tag size="mini" :type="getSourceTypeTag(scope.row.source)">
            {{ getSourceTypeText(scope.row.source) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="warehouseName" label="仓库" width="120" show-overflow-tooltip></el-table-column>
      <el-table-column prop="areaName" label="库区" width="100" show-overflow-tooltip>
        <template slot-scope="scope">
          {{ scope.row.areaName || "-" }}
        </template>
      </el-table-column>
      <el-table-column prop="storageLocationName" label="储位" width="120" show-overflow-tooltip>
        <template slot-scope="scope">
          {{ scope.row.storageLocationName || "-" }}
        </template>
      </el-table-column>
      <el-table-column prop="operatorName" label="操作人" width="100" show-overflow-tooltip></el-table-column>
      <el-table-column prop="createTime" label="操作时间" width="160" align="center">
        <template slot-scope="scope">
          {{ $_common.formatDate(scope.row.createTime) }}
        </template>
      </el-table-column>
      <el-table-column prop="sourceNo" label="关联单号" width="160" align="center">
        <template slot-scope="scope">
          <div style="height: 34px; line-height: 34px">
            <el-button v-if="scope.row.sourceNo" type="text" @click="viewRelatedOrder(scope.row)">
              {{ scope.row.sourceNo }}
            </el-button>
            <span v-else>-</span>
          </div>
        </template>
      </el-table-column>
    </el-table>
    <FooterPage
      :page-size="queryParams.pageSize"
      :total-page.sync="total"
      :current-page.sync="queryParams.page"
      @pageChange="pageChange"
      @sizeChange="sizeChange"
    />
  </ContainerQuery>
</template>

<script>
import { getInventoryChangeRecords } from "@/api/SupplierConsignment";
import FooterPage from "@/component/common/FooterPage";
import SelectSupplier from "@/component/common/SelectSupplier";

export default {
  name: "InventoryFlowing",
  components: {
    FooterPage,
    SelectSupplier,
  },

  data() {
    return {
      // 遮罩层
      loading: false,
      // 总条数
      total: 0,
      // 记录列表
      recordList: [],
      // 查询参数
      queryParams: {
        page: 1,
        pageSize: 10,
        supplierId: null,
        materielId: null,
        keyword: "",
        actionType: "",
        source: "",
        startTime: "",
        endTime: "",
      },
      // 日期范围
      dateRange: [],
      // 当前选中的供应商名称
      currentSupplierName: "",
    };
  },
  created() {
    // 从路由参数中获取供应商ID和商品ID
    const supplierId = this.$route.query.supplierId;
    const materielId = this.$route.query.materielId;

    if (supplierId) {
      this.queryParams.supplierId = parseInt(supplierId);
    }

    if (materielId) {
      this.queryParams.materielId = parseInt(materielId);
    }

    this.getData();
  },
  methods: {
    // 获取记录列表
    getData() {
      this.loading = true;
      getInventoryChangeRecords(this.queryParams)
        .then((response) => {
          this.recordList = response.data;
          this.total = response.pageTotal;
          this.loading = false;
        })
        .catch(() => {
          this.loading = false;
        });
    },

    // 切页
    pageChange(val) {
      this.queryParams.page = val;
      this.getData();
    },

    // 每页数据大小改变
    sizeChange(val) {
      this.queryParams.pageSize = val;
      this.pageChange(1);
    },

    // 清除供应商选择
    clearSupplier() {
      this.queryParams.supplierId = "";
      this.currentSupplierName = "";
      this.pageChange(1);
    },

    // 供应商选择变更
    handleSupplierChange(supplierId, supplierRow) {
      if (supplierId && supplierRow && supplierRow.length > 0) {
        this.currentSupplierName = supplierRow[0].title || "";
      } else {
        this.currentSupplierName = "";
      }
      this.pageChange(1);
    },

    // 处理日期范围变化
    handleDateRangeChange(val) {
      if (val && val.length === 2) {
        this.queryParams.startTime = Math.floor(val[0] / 1000);
        this.queryParams.endTime = Math.floor(val[1] / 1000) + 86399;
      } else {
        this.queryParams.startTime = "";
        this.queryParams.endTime = "";
      }
      this.pageChange(1);
    },

    // 查看关联单据
    viewRelatedOrder(row) {
      // 根据来源类型跳转到不同的页面
      if (row.source === 7 || row.source === 8) {
        // 调拨相关
        this.$router.push({
          path: "/Stock/AllocateList",
          query: { orderNo: row.sourceNo },
        });
      } else if (row.source === 1) {
        // 销售订单
        this.$router.push({
          path: "/Order/OrderList",
          query: { orderNo: row.sourceNo },
        });
      } else if (row.source === 2) {
        // 采购订单
        this.$router.push({
          path: "/Purchase/PurchaseList",
          query: { orderNo: row.sourceNo },
        });
      }
    },

    // 获取操作类型标签样式
    getActionTypeTag(actionType) {
      const typeMap = {
        4: "danger", // 出库
        5: "success", // 入库
      };
      return typeMap[actionType] || "";
    },

    // 获取操作类型文本
    getActionTypeText(actionType) {
      const typeMap = {
        4: "出库",
        5: "入库",
      };
      return typeMap[actionType] || actionType;
    },

    // 获取来源类型标签样式
    getSourceTypeTag(source) {
      const typeMap = {
        1: "primary", // 销售订单
        2: "success", // 采购订单
        6: "warning", // 销售退货
        7: "info", // 调拨库存
        8: "danger", // 调拨出库
      };
      return typeMap[source] || "";
    },

    // 获取来源类型文本
    getSourceTypeText(source) {
      const typeMap = {
        1: "销售订单",
        2: "采购订单",
        6: "销售退货",
        7: "调拨库存",
        8: "调拨出库",
      };
      return typeMap[source] || source;
    },
  },
};
</script>

<style lang="scss" scoped>
.product-info {
  display: flex;
  align-items: center;

  .product-image {
    width: 40px;
    height: 40px;
    margin-right: 10px;
    object-fit: cover;
    border-radius: 4px;
  }

  .product-text {
    flex: 1;
    min-width: 0;

    .product-name {
      font-size: 14px;
      color: #303133;
      margin-bottom: 2px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    .sku-name {
      font-size: 12px;
      color: #909399;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
}

.text-red {
  color: #f56c6c;
  font-weight: 500;
}

.text-green {
  color: #67c23a;
  font-weight: 500;
}
</style>

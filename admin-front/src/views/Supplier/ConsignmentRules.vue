<template>
  <ContainerQuery>
    <div slot="left">
      <el-button type="primary" @click="handleAdd">新增规则</el-button>
      <el-button type="success" @click="handlePriority">设置优先级</el-button>
      <el-button type="warning" @click="handleImport">批量导入</el-button>
      <el-button @click="handleExport">导出</el-button>
      <el-button type="info" @click="handleTemplate">下载模板</el-button>
    </div>
    <el-form slot="more" :inline="true" size="small">
      <el-form-item>
        <el-input
          v-model="queryParams.ruleName"
          clearable
          style="width: 320px"
          placeholder="请输入规则名称"
          @keyup.enter.native="pageChange(1)"
          @clear="pageChange(1)"
        >
          <el-button slot="append" icon="el-icon-search" @click="pageChange(1)"></el-button>
        </el-input>
      </el-form-item>
      <el-form-item>
        <el-select
          v-model="queryParams.supplierId"
          filterable
          remote
          reserve-keyword
          placeholder="请输入供应商名称"
          :remote-method="remoteSupplierMethod"
          :loading="supplierLoading"
          clearable
          style="width: 220px"
          @change="handleSupplierChange"
        >
          <el-option
            v-for="item in supplierOptions"
            :key="item.supplierId"
            :label="item.title"
            :value="item.supplierId"
          >
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-select
          v-model="queryParams.status"
          placeholder="请选择状态"
          clearable
          style="width: 220px"
          @change="pageChange(1)"
        >
          <el-option label="全部" value=""></el-option>
          <el-option label="启用" :value="5"></el-option>
          <el-option label="禁用" :value="4"></el-option>
        </el-select>
      </el-form-item>
    </el-form>
    <el-table v-loading="loading" :data="rulesList" style="width: 100%" row-key="id">
      <el-table-column prop="id" label="规则ID" width="80" align="center"></el-table-column>
      <el-table-column prop="priority" label="优先级" width="80" align="center"></el-table-column>
      <el-table-column prop="ruleName" label="规则名称" min-width="150"></el-table-column>
      <el-table-column prop="supplierName" label="供应商" min-width="150"></el-table-column>
      <el-table-column prop="ruleType" label="规则类型" width="100" align="center">
        <template slot-scope="scope">
          <el-tag :type="getRuleTypeTag(scope.row.ruleType)">{{ getRuleTypeText(scope.row.ruleType) }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="commissionRate" label="分账方式" width="120" align="center">
        <template>
          <span>固定金额</span>
        </template>
      </el-table-column>
      <el-table-column prop="startTime" label="生效开始日期" width="120" align="center">
        <template slot-scope="scope">
          {{ scope.row.startTime ? $_common.formatDate(scope.row.startTime, "yyyy-MM-dd") : "-" }}
        </template>
      </el-table-column>
      <el-table-column prop="endTime" label="生效结束日期" width="120" align="center">
        <template slot-scope="scope">
          {{ scope.row.endTime ? $_common.formatDate(scope.row.endTime, "yyyy-MM-dd") : "-" }}
        </template>
      </el-table-column>
      <el-table-column prop="status" label="状态" width="80" align="center">
        <template slot-scope="scope">
          <el-switch
            v-model="scope.row.status"
            :active-value="5"
            :inactive-value="4"
            @change="handleStatusChange(scope.row)"
          ></el-switch>
        </template>
      </el-table-column>
      <el-table-column prop="createTime" label="创建时间" width="160" align="center">
        <template slot-scope="scope">
          {{ $_common.formatDate(scope.row.createTime) }}
        </template>
      </el-table-column>
      <el-table-column label="操作" width="160" fixed="right">
        <template slot-scope="scope">
          <div style="height: 34px; line-height: 34px">
            <el-button type="text" @click="handleEdit(scope.row)">编辑</el-button>
            <el-button type="text" @click="handleDelete(scope.row)">删除</el-button>
            <el-button type="text" @click="handleCheckConflict(scope.row)">检测冲突</el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>
    <FooterPage
      :page-size="queryParams.pageSize"
      :total-page.sync="total"
      :current-page.sync="queryParams.page"
      @pageChange="pageChange"
      @sizeChange="sizeChange"
    />

    <!-- 规则表单对话框 -->
    <el-dialog
      :title="dialogTitle"
      :visible.sync="dialogVisible"
      width="950px"
      :close-on-click-modal="false"
      :append-to-body="true"
      :show-close="true"
      :before-close="handleDialogClose"
    >
      <ConsignmentRuleForm
        ref="ruleFormComponent"
        :rule-id="currentRuleId"
        :is-dialog="true"
        @save-success="handleSaveSuccess"
        @cancel="handleDialogClose"
      />
      <span slot="footer" class="dialog-footer"></span>
    </el-dialog>

    <!-- 优先级设置对话框 -->
    <el-dialog title="设置规则优先级" :visible.sync="priorityDialogVisible" width="600px">
      <el-alert title="拖动规则调整优先级顺序，越靠上优先级越高" type="info" :closable="false" show-icon></el-alert>
      <div class="priority-list">
        <draggable v-model="priorityRules" handle=".drag-handle">
          <div v-for="(item, index) in priorityRules" :key="item.id" class="priority-item">
            <div class="drag-handle">
              <i class="el-icon-rank"></i>
            </div>
            <div class="priority-index">{{ index + 1 }}</div>
            <div class="priority-content">
              <div class="priority-name">{{ item.ruleName }}</div>
              <div class="priority-supplier">{{ item.supplierName }}</div>
            </div>
          </div>
        </draggable>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="priorityDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="submitPriority">确 定</el-button>
      </div>
    </el-dialog>

    <!-- 导入对话框 -->
    <el-dialog title="批量导入规则" :visible.sync="importDialogVisible" width="500px">
      <el-upload
        class="upload-demo"
        drag
        action="#"
        :http-request="handleUpload"
        :before-upload="beforeUpload"
        :limit="1"
        :file-list="fileList"
      >
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
        <div slot="tip" class="el-upload__tip">只能上传xlsx文件，且不超过10MB</div>
      </el-upload>
      <div slot="footer" class="dialog-footer">
        <el-button @click="importDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="submitImport">确 定</el-button>
      </div>
    </el-dialog>

    <!-- 冲突检测结果对话框 -->
    <el-dialog
      title="规则冲突检测结果"
      :visible.sync="conflictDialogVisible"
      width="700px"
      :before-close="handleCloseConflictDialog"
    >
      <div v-loading="loading">
        <div v-if="conflictList.length === 0" class="no-conflict">
          <el-result icon="success" title="未检测到冲突" sub-title="当前规则与其他规则不存在冲突"></el-result>
        </div>
        <div v-else>
          <el-alert
            title="检测到以下规则存在冲突，请调整规则优先级或修改规则内容"
            type="warning"
            :closable="false"
            show-icon
          ></el-alert>
          <el-table :data="conflictList" border style="width: 100%; margin-top: 20px">
            <el-table-column prop="id" label="规则ID" width="80" align="center"></el-table-column>
            <el-table-column prop="ruleName" label="规则名称" min-width="150"></el-table-column>
            <el-table-column prop="supplierName" label="供应商" min-width="150"></el-table-column>
            <el-table-column prop="commissionRate" label="分账方式" width="100" align="center">
              <template> 固定金额 </template>
            </el-table-column>
            <el-table-column prop="priority" label="优先级" width="80" align="center"></el-table-column>
            <el-table-column label="操作" width="160" fixed="right">
              <template slot-scope="scope">
                <div style="height: 34px; line-height: 34px">
                  <el-button type="text" @click="handleEdit(scope.row)">编辑</el-button>
                </div>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="handleCloseConflictDialog">关 闭</el-button>
        <el-button type="primary" @click="handlePriority">设置优先级</el-button>
      </div>
    </el-dialog>
  </ContainerQuery>
</template>

<script>
import {
  checkRuleConflicts,
  deleteConsignmentRule,
  exportConsignmentRules,
  exportRuleTemplate,
  getConsignmentRules,
  getConsignmentSuppliers,
  importConsignmentRules,
  setRulePriorities,
  updateConsignmentRuleStatus,
} from "@/api/SupplierConsignment";
import draggable from "vuedraggable";
import ConsignmentRuleForm from "./ConsignmentRuleForm.vue";

export default {
  name: "ConsignmentRules",
  components: {
    draggable,
    ConsignmentRuleForm,
  },
  data() {
    return {
      // 遮罩层
      loading: false,
      // 总条数
      total: 0,
      // 规则列表
      rulesList: [],
      // 查询参数
      queryParams: {
        page: 1,
        pageSize: 10,
        supplierId: null,
        ruleName: "",
        status: "",
      },
      // 供应商选项
      supplierOptions: [],
      // 供应商加载状态
      supplierLoading: false,
      // 当前选中的供应商名称
      currentSupplierName: "",
      // 当前选中的规则类型
      currentRuleType: 4, // 默认为固定金额模式

      // 对话框标题
      dialogTitle: "",
      // 对话框可见性
      dialogVisible: false,
      // 当前编辑的规则ID
      currentRuleId: null,

      // 优先级对话框可见性
      priorityDialogVisible: false,
      // 优先级规则列表
      priorityRules: [],

      // 导入对话框可见性
      importDialogVisible: false,
      // 上传文件列表
      fileList: [],
      // 上传文件对象
      uploadFile: null,

      // 冲突检测对话框可见性
      conflictDialogVisible: false,
      // 冲突规则列表
      conflictList: [],
    };
  },
  created() {
    this.getData();
    this.remoteSupplierMethod("");
  },
  methods: {
    // 获取规则列表
    getData() {
      this.loading = true;
      getConsignmentRules(this.queryParams)
        .then((response) => {
          this.rulesList = response.data;
          this.total = response.pageTotal;
          this.loading = false;
        })
        .catch(() => {
          this.loading = false;
        });
    },

    // 切页
    pageChange(val) {
      this.queryParams.page = val;
      this.getData();
    },

    // 每页数据大小改变
    sizeChange(val) {
      this.queryParams.pageSize = val;
      this.pageChange(1);
    },

    // 远程搜索供应商
    remoteSupplierMethod(query) {
      this.supplierLoading = true;
      getConsignmentSuppliers({
        keyword: query || "",
        page: 1,
        pageSize: 20,
      })
        .then((response) => {
          this.supplierOptions = response.data;
          this.supplierLoading = false;
        })
        .catch(() => {
          this.supplierLoading = false;
        });
    },

    // 供应商选择变更
    handleSupplierChange(supplierId) {
      if (supplierId) {
        const supplier = this.supplierOptions.find((item) => item.supplierId === supplierId);
        if (supplier) {
          this.currentSupplierName = supplier.title;
        }
      }
      this.pageChange(1);
    },

    // 新增规则
    handleAdd() {
      this.dialogTitle = "新增分账规则";
      this.currentRuleId = null;
      this.dialogVisible = true;
    },

    // 编辑规则
    handleEdit(row) {
      this.dialogTitle = "编辑分账规则";
      this.currentRuleId = row.id;
      this.dialogVisible = true;
    },

    // 处理保存成功事件
    handleSaveSuccess() {
      // 使用handleDialogClose方法关闭对话框并重置表单
      this.handleDialogClose(() => {});
      this.getData();
    },

    // 删除规则
    handleDelete(row) {
      this.$confirm(`确认删除规则 "${row.ruleName}" 吗?`, "警告", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        deleteConsignmentRule(row.id).then(() => {
          this.$message.success("删除成功");
          this.getData();
        });
      });
    },

    // 状态变更
    handleStatusChange(row) {
      updateConsignmentRuleStatus(row.id, row.status)
        .then(() => {
          this.$message.success(`${row.status === 5 ? "启用" : "禁用"}成功`);
        })
        .catch(() => {
          row.status = row.status === 5 ? 4 : 5;
        });
    },

    // 设置优先级
    handlePriority() {
      getConsignmentRules({ pageSize: 100 }).then((response) => {
        this.priorityRules = response.data.sort((a, b) => a.priority - b.priority);
        this.priorityDialogVisible = true;
      });
    },

    // 提交优先级
    submitPriority() {
      const ruleIds = this.priorityRules.map((item) => item.id);
      setRulePriorities(ruleIds).then(() => {
        this.$message.success("优先级设置成功");
        this.priorityDialogVisible = false;
        this.getData();
      });
    },

    // 检测冲突
    handleCheckConflict(row) {
      // 先清空之前的冲突列表数据
      this.conflictList = [];
      // 显示加载状态
      this.loading = true;

      checkRuleConflicts(row.id)
        .then((response) => {
          this.conflictList = response.data || [];
          this.conflictDialogVisible = true;
          this.loading = false;
        })
        .catch(() => {
          this.loading = false;
        });
    },

    // 处理导入
    handleImport() {
      this.fileList = [];
      this.uploadFile = null;
      this.importDialogVisible = true;
    },

    // 上传前检查
    beforeUpload(file) {
      const isExcel = file.type === "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
      const isLt10M = file.size / 1024 / 1024 < 10;

      if (!isExcel) {
        this.$message.error("只能上传xlsx格式的文件!");
      }
      if (!isLt10M) {
        this.$message.error("文件大小不能超过10MB!");
      }

      return isExcel && isLt10M;
    },

    // 处理上传
    handleUpload(options) {
      this.uploadFile = options.file;
      this.fileList = [{ name: options.file.name, url: "" }];
    },

    // 提交导入
    submitImport() {
      if (!this.uploadFile) {
        this.$message.warning("请先上传文件");
        return;
      }

      const formData = new FormData();
      formData.append("file", this.uploadFile);

      importConsignmentRules(formData).then((response) => {
        this.$message.success(`导入成功，成功导入${response.data.successCount}条规则`);
        this.importDialogVisible = false;
        this.getData();
      });
    },

    // 导出
    handleExport() {
      this.$confirm("是否确认导出所有分账规则数据?", "警告", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          this.$message.success("正在导出分账规则数据，请稍候...");
          return exportConsignmentRules(this.queryParams);
        })
        .then((response) => {
          this.downloadFile(response, `分账规则_${new Date().getTime()}.xlsx`);
        });
    },

    // 下载模板
    handleTemplate() {
      exportRuleTemplate().then((response) => {
        this.downloadFile(response, "分账规则导入模板.xlsx");
      });
    },

    // 下载文件
    downloadFile(res, fileName) {
      const blob = new Blob([res], { type: "application/vnd.ms-excel" });
      const link = document.createElement("a");
      link.href = URL.createObjectURL(blob);
      link.download = fileName;
      link.click();
      URL.revokeObjectURL(link.href);
    },

    // 获取规则类型文本（简化为只支持固定金额）
    getRuleTypeText() {
      return "固定金额";
    },

    // 获取规则类型标签样式（简化为只支持固定金额）
    getRuleTypeTag() {
      return "primary";
    },



    // 处理对话框关闭
    handleDialogClose(done) {
      // 关闭对话框前重置表单组件
      if (this.$refs.ruleFormComponent) {
        this.$refs.ruleFormComponent.resetForm();
      }
      // 重置当前编辑的规则ID
      this.currentRuleId = null;
      // 关闭对话框
      this.dialogVisible = false;
      done();
    },

    // 处理冲突对话框关闭
    handleCloseConflictDialog() {
      // 关闭对话框
      this.conflictDialogVisible = false;
      // 清空冲突列表数据
      this.conflictList = [];
    },
  },
};
</script>

<style lang="scss" scoped>
.operation-buttons {
  margin: 20px 0;
  display: flex;
  gap: 10px;
}

.rate-suffix {
  margin-left: 5px;
}

.priority-list {
  margin-top: 20px;
  max-height: 400px;
  overflow-y: auto;
}

.priority-item {
  display: flex;
  align-items: center;
  padding: 10px;
  border: 1px solid #ebeef5;
  margin-bottom: 10px;
  background-color: #fff;

  &:hover {
    background-color: #f5f7fa;
  }

  .drag-handle {
    cursor: move;
    padding: 0 10px;
    color: #909399;
  }

  .priority-index {
    width: 30px;
    height: 30px;
    line-height: 30px;
    text-align: center;
    background-color: #409eff;
    color: #fff;
    border-radius: 50%;
    margin-right: 15px;
  }

  .priority-content {
    flex: 1;

    .priority-name {
      font-weight: bold;
      margin-bottom: 5px;
    }

    .priority-supplier {
      font-size: 12px;
      color: #909399;
    }
  }
}

.no-conflict {
  padding: 20px 0;
}
</style>

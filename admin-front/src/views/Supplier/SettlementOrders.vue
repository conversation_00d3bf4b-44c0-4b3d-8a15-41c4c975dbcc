<template>
  <ContainerQuery>
    <div slot="tip" class="page-tip-div" style="margin-top: 0">
      <i class="el-icon-info"></i>
      <span>温馨提示：</span>
      <span>1、此页面显示所有结算单信息，汇总了特定时间范围内供应商的分账明细；</span>
      <span>2、每条记录代表一个结算单，包含该结算单下所有分账明细的汇总信息；</span>
      <span>3、可通过筛选条件查询特定结算单。</span>
    </div>
    <div slot="left">
      <el-button size="small" type="primary" @click="showGenerateDialog">生成结算</el-button>
      <el-button size="small" type="primary" plain @click="exportData">导出</el-button>
      <el-button
        v-if="$accessCheck($Access.SettlementOrderAudit)"
        size="small"
        type="success"
        :disabled="selectedRows.length === 0"
        @click="handleBatchAudit"
      >
        批量审核 ({{ selectedRows.length }})
      </el-button>
    </div>
    <el-form v-if="accessSearch" slot="more" size="small" :inline="true">
      <el-form-item>
        <el-input
          v-model="queryParams.settlementNo"
          clearable
          style="width: 200px"
          placeholder="结算单号"
          @keyup.enter.native="handleQuery"
          @clear="handleQuery"
        >
          <el-button slot="append" icon="el-icon-search" @click="handleQuery"></el-button>
        </el-input>
      </el-form-item>
      <el-form-item>
        <el-input
          v-model="queryParams.supplierName"
          clearable
          style="width: 200px"
          placeholder="供应商名称"
          @keyup.enter.native="handleQuery"
          @clear="handleQuery"
        >
        </el-input>
      </el-form-item>
      <el-form-item>
        <SelectSupplier
          v-model="queryParams.supplierId"
          placeholder="选择供应商"
          @clear="clearSupplier"
          @change="handleSupplierChange"
        />
      </el-form-item>
      <el-form-item>
        <el-date-picker
          v-model="dateRange"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          value-format="timestamp"
          style="width: 220px"
          @change="handleDateRangeChange"
        >
        </el-date-picker>
      </el-form-item>
    </el-form>
    <el-table
      v-loading="loading"
      :data="ordersList"
      style="width: 100%"
      :border="true"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" :selectable="(row) => row.settlementStatus === 5"></el-table-column>
      <el-table-column prop="settlementNo" label="结算单号" min-width="160" fixed="left"></el-table-column>
      <el-table-column prop="supplierName" label="供应商名称" min-width="160"></el-table-column>
      <el-table-column prop="settlementAmount" label="结算单总金额" min-width="120" align="right">
        <template slot-scope="scope">
          {{ $_common.formatNub(scope.row.settlementAmount) }}
        </template>
      </el-table-column>
      <el-table-column prop="settlementStatus" label="结算状态" min-width="100" align="center">
        <template slot-scope="scope">
          <el-tag :type="getStatusType(scope.row.settlementStatus)">
            {{ getStatusText(scope.row.settlementStatus) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="settlementTime" label="结算时间" min-width="160">
        <template slot-scope="scope">
          {{ $_common.formatDate(scope.row.settlementTime) }}
        </template>
      </el-table-column>
      <el-table-column prop="createTime" label="创建时间" min-width="160">
        <template slot-scope="scope">
          {{ $_common.formatDate(scope.row.createTime) }}
        </template>
      </el-table-column>
      <el-table-column label="操作" min-width="160" align="center">
        <template slot-scope="scope">
          <el-button size="mini" type="text" @click="handleDetail(scope.row)">明细</el-button>
          <el-button
            v-if="scope.row.settlementStatus === 5 && $accessCheck($Access.SettlementOrderAudit)"
            size="mini"
            type="text"
            @click="handleAudit(scope.row)"
          >
            审核
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <FooterPage
      :page-size="pageSize"
      :total-page.sync="total"
      :current-page.sync="page"
      @pageChange="pageChange"
      @sizeChange="sizeChange"
    ></FooterPage>

    <!-- 生成结算对话框 -->
    <el-dialog title="手动生成结算" :visible.sync="generateDialogVisible" width="500px" :close-on-click-modal="false">
      <el-form ref="generateForm" :model="generateForm" :rules="generateRules" label-width="120px">
        <el-form-item label="供应商" prop="supplierId">
          <SelectSupplier
            v-model="generateForm.supplierId"
            placeholder="请选择供应商"
            style="width: 100%"
            @change="handleGenerateSupplierChange"
          />
        </el-form-item>
        <el-form-item label="结算周期" prop="dateRange">
          <el-date-picker
            v-model="generateForm.dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="yyyy-MM-dd"
            value-format="yyyy-MM-dd"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="备注">
          <el-input v-model="generateForm.remark" type="textarea" :rows="3" placeholder="请输入备注信息（可选）" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="generateDialogVisible = false">取消</el-button>
        <el-button type="primary" :loading="generateLoading" @click="handleGenerateSettlement">
          {{ generateLoading ? "生成中..." : "确认生成" }}
        </el-button>
      </div>
    </el-dialog>
  </ContainerQuery>
</template>

<script>
import { auditSettlementOrder, getSettlementOrders, manualGenerateSettlement } from "@/api/SupplierConsignment";
import FooterPage from "@/component/common/FooterPage";
import SelectSupplier from "@/component/common/SelectSupplier";

export default {
  name: "SettlementOrders",
  components: {
    FooterPage,
    SelectSupplier,
  },

  data() {
    return {
      // 遮罩层
      loading: false,
      // 结算单列表
      ordersList: [],
      // 查询参数
      queryParams: {
        settlementNo: "",
        supplierName: "",
        supplierId: null,
        startDate: "",
        endDate: "",
      },
      // 分页参数
      total: 0,
      page: 1,
      pageSize: 10,
      // 日期范围
      dateRange: [],

      // 权限控制
      accessSearch: true,

      // 生成结算对话框相关
      generateDialogVisible: false,
      generateLoading: false,
      generateForm: {
        supplierId: null,
        dateRange: [],
        remark: "",
      },
      generateRules: {
        supplierId: [{ required: true, message: "请选择供应商", trigger: "change" }],
        dateRange: [{ required: true, message: "请选择结算周期", trigger: "change" }],
      },

      // 审核相关
      auditLoading: false,
      selectedRows: [], // 选中的行数据
    };
  },
  created() {
    // 从路由参数中获取结算单号
    const settlementNo = this.$route.query.settlementNo;
    if (settlementNo) {
      this.queryParams.settlementNo = settlementNo;
    }

    this.getList();
  },
  methods: {
    // 获取结算单列表
    getList() {
      this.loading = true;
      const params = {
        page: this.page,
        pageSize: this.pageSize,
        ...this.queryParams,
      };
      getSettlementOrders(params)
        .then((response) => {
          this.ordersList = response.data;
          this.total = response.pageTotal;
          this.loading = false;

          // 如果是从路由参数中获取的结算单号，自动打开详情
          if (this.$route.query.settlementNo && this.ordersList.length > 0) {
            this.handleDetail(this.ordersList[0]);
            // 清除路由参数，避免刷新页面再次自动打开
            this.$router.replace({ path: this.$route.path });
          }
        })
        .catch(() => {
          this.loading = false;
        });
    },

    // 分页改变
    pageChange(val) {
      this.page = val;
      this.getList();
    },

    // 每页数据大小改变
    sizeChange(val) {
      this.pageSize = val;
      this.pageChange(1);
    },

    // 清除供应商选择
    clearSupplier() {
      this.queryParams.supplierId = "";
      this.pageChange(1);
    },

    // 供应商选择变更
    handleSupplierChange() {
      this.pageChange(1);
    },

    // 处理日期范围变化
    handleDateRangeChange(val) {
      if (val && val.length) {
        this.queryParams.startDate = parseInt(val[0] / 1000);
        this.queryParams.endDate = parseInt(val[1] / 1000) + 86399;
      } else {
        this.queryParams.startDate = "";
        this.queryParams.endDate = "";
      }
      this.pageChange(1);
    },

    // 导出数据
    exportData() {
      this.$message.info("导出功能开发中...");
    },

    // 查询按钮
    handleQuery() {
      this.pageChange(1);
    },

    // 重置查询
    resetQuery() {
      this.dateRange = [];
      this.queryParams = {
        settlementNo: "",
        supplierName: "",
        supplierId: null,
        startDate: "",
        endDate: "",
      };
      this.pageChange(1);
    },

    // 查看详情
    handleDetail(row) {
      // 跳转到结算单详情页面或分账明细列表页面
      this.$router.push({
        path: "/Supplier/SettlementDetails",
        query: { settlementNo: row.settlementNo },
      });
    },

    // 获取结算状态文本
    getStatusText(status) {
      const statusMap = {
        1: "待结算",
        2: "结算中",
        3: "已结算",
        4: "结算失败",
        5: "待审核",
      };
      return statusMap[status] || "未知状态";
    },

    // 获取结算状态类型
    getStatusType(status) {
      const typeMap = {
        1: "info",
        2: "warning",
        3: "success",
        4: "danger",
        5: "warning",
      };
      return typeMap[status] || "";
    },

    // 获取结算周期文本
    getCycleText(cycle) {
      const cycleMap = {
        1: "月结",
        2: "季结",
        3: "半年结",
        4: "年结",
      };
      return cycleMap[cycle] || "未设置";
    },

    // 显示生成结算对话框
    showGenerateDialog() {
      this.generateDialogVisible = true;
      this.resetGenerateForm();
    },

    // 重置生成结算表单
    resetGenerateForm() {
      this.generateForm = {
        supplierId: null,
        dateRange: [],
        remark: "",
      };
      if (this.$refs.generateForm) {
        this.$refs.generateForm.clearValidate();
      }
    },

    // 供应商选择变更（生成结算表单）
    handleGenerateSupplierChange(supplierId) {
      this.generateForm.supplierId = supplierId;
    },

    // 处理生成结算
    handleGenerateSettlement() {
      this.$refs.generateForm.validate((valid) => {
        if (valid) {
          this.confirmGenerateSettlement();
        } else {
          this.$message.error("请完善表单信息");
          return false;
        }
      });
    },

    // 确认生成结算
    confirmGenerateSettlement() {
      this.$confirm("确认生成结算单？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          this.executeGenerateSettlement();
        })
        .catch(() => {
          this.$message.info("已取消生成");
        });
    },

    // 执行生成结算
    executeGenerateSettlement() {
      this.generateLoading = true;

      // 时间参数标准化处理
      const settlementStartTime = this.formatDateToTimestamp(this.generateForm.dateRange[0], false);
      const settlementEndTime = this.formatDateToTimestamp(this.generateForm.dateRange[1], true);

      // 验证时间戳格式
      if (!this.validateTimestamp(settlementStartTime) || !this.validateTimestamp(settlementEndTime)) {
        this.generateLoading = false;
        this.$message.error("时间参数格式不正确");
        return;
      }

      // 验证时间范围
      if (settlementStartTime >= settlementEndTime) {
        this.generateLoading = false;
        this.$message.error("开始时间必须小于结束时间");
        return;
      }

      const params = {
        supplierId: this.generateForm.supplierId,
        settlementStartTime: settlementStartTime, // Unix时间戳（秒）
        settlementEndTime: settlementEndTime, // Unix时间戳（秒）
        remark: this.generateForm.remark || "手动生成结算单",
      };

      console.log("结算单生成参数:", {
        ...params,
        settlementStartTimeFormatted: new Date(settlementStartTime * 1000).toLocaleString(),
        settlementEndTimeFormatted: new Date(settlementEndTime * 1000).toLocaleString(),
      });

      manualGenerateSettlement(params)
        .then((response) => {
          this.generateLoading = false;
          if (response.errorcode === 0) {
            this.$message.success("结算单生成成功");
            this.generateDialogVisible = false;
            this.getList(); // 刷新列表
          } else {
            this.$message.error(response.msg || "生成结算单失败");
          }
        })
        .catch((error) => {
          this.generateLoading = false;
          this.$message.error("生成结算单失败：" + (error.message || "网络错误"));
        });
    },

    /**
     * 格式化日期为Unix时间戳
     * @param {string} dateStr - 日期字符串 (格式: yyyy-MM-dd)
     * @param {boolean} isEndDate - 是否为结束日期
     * @returns {number|null} - Unix时间戳（秒级精度的整数）
     */
    formatDateToTimestamp(dateStr, isEndDate = false) {
      if (!dateStr) return null;

      const date = new Date(dateStr);

      if (isEndDate) {
        // 结束时间设置为当天的23:59:59
        date.setHours(23, 59, 59, 0);
      } else {
        // 开始时间设置为当天的00:00:00
        date.setHours(0, 0, 0, 0);
      }

      // 转换为Unix时间戳（秒）
      return Math.floor(date.getTime() / 1000);
    },

    /**
     * 验证时间戳格式是否正确
     * @param {number} timestamp - Unix时间戳
     * @returns {boolean} - 是否为有效的时间戳
     */
    validateTimestamp(timestamp) {
      // 检查是否为数字且为整数
      if (typeof timestamp !== "number" || !Number.isInteger(timestamp)) {
        return false;
      }

      // 检查时间戳范围（1970年到2100年之间）
      const minTimestamp = 0; // 1970-01-01
      const maxTimestamp = 4102444800; // 2100-01-01

      return timestamp >= minTimestamp && timestamp <= maxTimestamp;
    },

    // 处理审核
    handleAudit(row) {
      this.$confirm(`确认审核结算单 "${row.settlementNo}" 吗？审核通过后将自动生成应付账单。`, "审核确认", {
        confirmButtonText: "确认审核",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          this.executeAudit(row);
        })
        .catch(() => {
          this.$message.info("已取消审核");
        });
    },

    // 执行审核
    executeAudit(row) {
      this.auditLoading = true;

      const params = {
        settlementId: row.id,
        settlementNo: row.settlementNo,
      };

      // 调用审核API
      this.auditSettlementOrder(params)
        .then((response) => {
          this.auditLoading = false;
          if (response.errorcode === 0) {
            this.$message.success("结算单审核成功，已生成应付账单");
            this.getList(); // 刷新列表
          } else {
            this.$message.error(response.msg || "审核失败");
          }
        })
        .catch((error) => {
          this.auditLoading = false;
          this.$message.error("审核失败：" + (error.message || "网络错误"));
        });
    },

    // 审核结算单API调用
    auditSettlementOrder(params) {
      return auditSettlementOrder(params);
    },

    // 处理表格选择变化
    handleSelectionChange(selection) {
      this.selectedRows = selection.filter((row) => row.settlementStatus === 5);
    },

    // 批量审核
    handleBatchAudit() {
      if (this.selectedRows.length === 0) {
        this.$message.warning("请选择要审核的结算单");
        return;
      }

      const pendingCount = this.selectedRows.length;
      this.$confirm(
        `确认批量审核选中的 ${pendingCount} 个结算单吗？审核通过后将自动生成对应的应付账单。`,
        "批量审核确认",
        {
          confirmButtonText: "确认审核",
          cancelButtonText: "取消",
          type: "warning",
        }
      )
        .then(() => {
          this.executeBatchAudit();
        })
        .catch(() => {
          this.$message.info("已取消批量审核");
        });
    },

    // 执行批量审核
    async executeBatchAudit() {
      this.auditLoading = true;
      let successCount = 0;
      let failCount = 0;
      const errors = [];

      try {
        // 使用单个API逐个审核（简化实现）
        for (const row of this.selectedRows) {
          try {
            const params = {
              settlementId: row.id,
              settlementNo: row.settlementNo,
            };

            const response = await auditSettlementOrder(params);
            if (response.errorcode === 0) {
              successCount++;
            } else {
              failCount++;
              errors.push(`${row.settlementNo}: ${response.msg}`);
            }
          } catch (error) {
            failCount++;
            errors.push(`${row.settlementNo}: ${error.message || "网络错误"}`);
          }
        }

        // 显示结果
        if (successCount > 0 && failCount === 0) {
          this.$message.success(`批量审核完成，成功审核 ${successCount} 个结算单`);
        } else if (successCount > 0 && failCount > 0) {
          this.$message.warning(`批量审核完成，成功 ${successCount} 个，失败 ${failCount} 个`);
          if (errors.length > 0) {
            console.error("审核失败详情:", errors);
          }
        } else {
          this.$message.error(`批量审核失败，所有 ${failCount} 个结算单审核失败`);
        }

        // 刷新列表
        this.getList();
        // 清空选择
        this.selectedRows = [];
      } finally {
        this.auditLoading = false;
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.page-tip-div {
  padding: 10px 16px;
  background-color: #f4f4f5;
  border-radius: 4px;
  margin-bottom: 16px;
  color: #606266;
  font-size: 14px;
  line-height: 1.5;

  i {
    color: #409eff;
    margin-right: 8px;
  }

  span {
    margin-right: 16px;
  }
}

.info-status {
  color: #909399;
}

.success-status {
  color: #67c23a;
}

.danger-status {
  color: #f56c6c;
}

.warning-status {
  color: #e6a23c;
}

.primary-status {
  color: #409eff;
}

.detail-header {
  margin-bottom: 20px;

  .detail-title {
    display: flex;
    align-items: center;
    margin-bottom: 15px;

    h3 {
      margin: 0;
      margin-right: 10px;
    }
  }

  .detail-info {
    display: flex;
    flex-wrap: wrap;

    .info-item {
      width: 33.33%;
      margin-bottom: 10px;

      .label {
        color: #909399;
      }
    }
  }
}

.detail-summary {
  margin-bottom: 20px;

  .summary-card {
    background-color: #f5f7fa;
    padding: 15px;
    border-radius: 4px;
    text-align: center;

    .summary-title {
      font-size: 14px;
      color: #606266;
      margin-bottom: 10px;
    }

    .summary-value {
      font-size: 20px;
      font-weight: bold;
      color: #409eff;
    }
  }
}

.detail-content {
  margin-top: 20px;
}
</style>

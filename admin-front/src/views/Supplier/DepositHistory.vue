<template>
  <ContainerQuery>
    <div slot="left">
      <el-button type="success" @click="handleExport">导出</el-button>
    </div>
    <el-form slot="more" :inline="true" size="small">
      <el-form-item>
        <SelectSupplier
          v-model="queryParams.supplierId"
          placeholder="请选择供应商"
          @clear="clearSupplier"
          @change="handleSupplierChange"
          @keyup.enter.native="handleQuery"
        >
          <el-button slot="append" icon="el-icon-search" @click="handleQuery"></el-button>
        </SelectSupplier>
      </el-form-item>
      <el-form-item>
        <el-select
          v-model="queryParams.operationType"
          placeholder="请选择流水类型"
          clearable
          multiple
          collapse-tags
          style="width: 220px"
          @change="pageChange(1)"
        >
          <el-option label="充值" :value="1"></el-option>
          <el-option label="扣除" :value="2"></el-option>
          <el-option label="冻结" :value="3"></el-option>
          <el-option label="解冻" :value="4"></el-option>
          <el-option label="结算扣款" :value="5"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-date-picker
          v-model="dateRange"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          value-format="timestamp"
          style="width: 220px"
          @change="handleDateRangeChange"
        >
        </el-date-picker>
      </el-form-item>
    </el-form>
    <el-table
      v-loading="loading"
      :data="historyList"
      style="width: 100%"
      :empty-text="queryParams.supplierId ? '暂无数据' : '请选择供应商'"
    >
      <el-table-column type="index" label="序号" width="60" align="center"></el-table-column>
      <el-table-column prop="no" label="流水号" width="120" align="center" show-overflow-tooltip></el-table-column>
      <el-table-column prop="operationType" label="类型" width="100" align="center">
        <template slot-scope="scope">
          <el-tag :type="getTypeTag(scope.row.operationType)" size="mini">{{
            getTypeText(scope.row.operationType)
          }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="amount" label="金额" width="120" align="right">
        <template slot-scope="scope">
          <span :class="{ 'text-red': scope.row.amount < 0, 'text-green': scope.row.amount > 0 }">
            {{ $_common.formatNub(scope.row.amount) }}
          </span>
        </template>
      </el-table-column>
      <el-table-column prop="balanceBefore" label="变动前余额" width="120" align="right">
        <template slot-scope="scope">
          {{ $_common.formatNub(scope.row.balanceBefore) }}
        </template>
      </el-table-column>
      <el-table-column prop="balanceAfter" label="变动后余额" width="120" align="right">
        <template slot-scope="scope">
          {{ $_common.formatNub(scope.row.balanceAfter) }}
        </template>
      </el-table-column>
      <el-table-column prop="createTime" label="操作时间" width="160" align="center">
        <template slot-scope="scope">
          {{ scope.row.createTime ? $_common.formatDate(scope.row.createTime) : "-" }}
        </template>
      </el-table-column>
      <el-table-column prop="operator" label="操作人" width="120" show-overflow-tooltip></el-table-column>
      <el-table-column prop="remark" label="备注" min-width="200" show-overflow-tooltip></el-table-column>
    </el-table>
    <FooterPage
      :page-size="queryParams.limit"
      :total-page.sync="total"
      :current-page.sync="queryParams.page"
      @pageChange="pageChange"
      @sizeChange="sizeChange"
    />
  </ContainerQuery>
</template>

<script>
import { exportDepositHistory, getDepositHistory } from "@/api/SupplierConsignment";
import FooterPage from "@/component/common/FooterPage";
import SelectSupplier from "@/component/common/SelectSupplier";

export default {
  name: "DepositHistory",
  components: {
    FooterPage,
    SelectSupplier,
  },
  data() {
    return {
      // 遮罩层
      loading: false,
      // 总条数
      total: 0,
      // 流水列表
      historyList: [],
      // 查询参数
      queryParams: {
        page: 1,
        limit: 10,
        supplierId: null,
        operationType: [],
        startDate: "",
        endDate: "",
      },
      // 日期范围
      dateRange: [],
      // 当前选中的供应商名称
      currentSupplierName: "",
    };
  },
  created() {
    this.getData();
  },
  methods: {
    // 获取流水列表
    getData() {
      if (!this.queryParams.supplierId) {
        return;
      }
      this.loading = true;

      // 创建一个新的查询参数对象，将operationType转换为后端期望的参数格式
      const params = { ...this.queryParams };
      if (params.operationType && params.operationType.length > 0) {
        params.operationTypes = params.operationType;
      }
      delete params.operationType;

      getDepositHistory(params)
        .then((response) => {
          this.historyList = response.data.list;
          this.total = response.data.total;
          this.loading = false;
        })
        .catch(() => {
          this.loading = false;
        });
    },

    // 查询按钮
    handleQuery() {
      this.pageChange(1);
    },

    // 切页
    pageChange(val) {
      this.queryParams.page = val;
      this.getData();
    },

    // 每页数据大小改变
    sizeChange(val) {
      this.queryParams.limit = val;
      this.pageChange(1);
    },

    // 清除供应商选择
    clearSupplier() {
      this.queryParams.supplierId = "";
      this.currentSupplierName = "";
      this.pageChange(1);
    },

    // 供应商选择变更
    handleSupplierChange(supplierId, supplierRow) {
      if (supplierId && supplierRow && supplierRow.length > 0) {
        this.currentSupplierName = supplierRow[0].title || "";
      } else {
        this.currentSupplierName = "";
      }
      this.pageChange(1);
    },

    // 处理日期范围变化
    handleDateRangeChange(val) {
      if (val && val.length === 2) {
        this.queryParams.startDate = parseInt(val[0] / 1000);
        this.queryParams.endDate = parseInt(val[1] / 1000) + 86399;
      } else {
        this.queryParams.startDate = "";
        this.queryParams.endDate = "";
      }
      this.pageChange(1);
    },

    // 导出
    handleExport() {
      this.$confirm("是否确认导出所有保证金流水数据?", "警告", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          this.$message.success("正在导出保证金流水数据，请稍候...");

          // 创建一个新的查询参数对象，将operationType转换为后端期望的参数格式
          const params = { ...this.queryParams };
          if (params.operationType && params.operationType.length > 0) {
            params.operationTypes = params.operationType;
          }
          delete params.operationType;

          return exportDepositHistory(params);
        })
        .then((response) => {
          this.downloadFile(response, `保证金流水_${new Date().getTime()}.xlsx`);
        });
    },

    // 下载文件
    downloadFile(res, fileName) {
      const blob = new Blob([res], { type: "application/vnd.ms-excel" });
      const link = document.createElement("a");
      link.href = URL.createObjectURL(blob);
      link.download = fileName;
      link.click();
      URL.revokeObjectURL(link.href);
    },

    // 获取类型标签样式
    getTypeTag(type) {
      const numTypeMap = {
        1: "success", // 充值
        2: "warning", // 扣除
        3: "danger", // 冻结
        4: "info", // 解冻
        5: "primary", // 结算扣款
      };
      return numTypeMap[type] || "";
    },

    // 获取类型文本
    getTypeText(type) {
      // 处理数字类型的 operationType
      const numTypeMap = {
        1: "充值",
        2: "扣除",
        3: "冻结",
        4: "解冻",
        5: "结算扣款",
      };
      return numTypeMap[type] || `未知类型(${type})`;
    },
  },
};
</script>

<style lang="scss" scoped>
.text-red {
  color: #f56c6c;
}

.text-green {
  color: #67c23a;
}
</style>

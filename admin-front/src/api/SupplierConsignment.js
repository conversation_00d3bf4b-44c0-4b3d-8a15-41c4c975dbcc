import request from "@/utils/request";

/**
 * 获取供应商代销配置
 * @param {Number} supplierId 供应商ID
 */
export function getConsignmentConfig(supplierId) {
  return request({
    url: `/Purchase/SupplierConsignment/getConsignmentConfig`,
    method: "post",
    data: {
      supplierId,
    },
  });
}

/**
 * 更新供应商代销配置
 * @param {Number} supplierId 供应商ID
 * @param {Object} configData 配置数据
 */
export function updateConsignmentConfig(supplierId, configData) {
  // 转换参数名称以匹配后端接口
  const data = {
    supplierId,
    enabled: configData.enabled,
    skuRateEnabled: configData.skuRateEnabled,
    settlementConfig: configData.settlementConfig,
    depositRequired: configData.depositRequired,
    minDepositAmount: configData.minDepositAmount,
  };

  // 过滤掉undefined的属性
  Object.keys(data).forEach((key) => {
    if (data[key] === undefined) {
      delete data[key];
    }
  });

  return request({
    url: `/Purchase/SupplierConsignment/updateConsignmentConfig`,
    method: "post",
    data,
  });
}

/**
 * 获取供应商保证金账户余额
 * @param {Number} supplierId 供应商ID
 */
export function getDepositAccount(supplierId) {
  return request({
    url: `/Purchase/SupplierDeposit/getDepositAccount`,
    method: "post",
    data: {
      supplierId,
    },
  });
}

/**
 * 充值保证金
 * @param {Number} supplierId 供应商ID
 * @param {Number} amount 金额
 * @param {String} remark 备注
 */
export function depositFunds(supplierId, amount, remark = "") {
  return request({
    url: `/Purchase/SupplierDeposit/deposit`,
    method: "post",
    data: {
      supplierId,
      amount,
      remark,
    },
  });
}

/**
 * 提取保证金
 * @param {Number} supplierId 供应商ID
 * @param {Number} amount 金额
 * @param {String} remark 备注
 */
export function withdrawFunds(supplierId, amount, remark = "") {
  return request({
    url: `/Purchase/SupplierDeposit/deduct`,
    method: "post",
    data: {
      supplierId,
      amount,
      remark,
    },
  });
}

/**
 * 获取所有启用代销的供应商列表
 */
export function getConsignmentSuppliers(data) {
  // 如果传入了limit参数但没有pageSize参数，将limit值赋给pageSize
  if (data && data.limit && !data.pageSize) {
    data.pageSize = data.limit;
    delete data.limit;
  }
  return request({
    url: `/Purchase/SupplierConsignment/getConsignmentSuppliers`,
    method: "post",
    data,
  });
}

/**
 * 冻结保证金
 * @param {Number} supplierId 供应商ID
 * @param {Number} amount 金额
 * @param {String} remark 备注
 */
export function freezeFunds(supplierId, amount, remark = "") {
  return request({
    url: `/Purchase/SupplierDeposit/freeze`,
    method: "post",
    data: {
      supplierId,
      amount,
      remark,
    },
  });
}

/**
 * 解冻保证金
 * @param {Number} supplierId 供应商ID
 * @param {Number} amount 金额
 * @param {String} remark 备注
 */
export function unfreezeFunds(supplierId, amount, remark = "") {
  return request({
    url: `/Purchase/SupplierDeposit/unfreeze`,
    method: "post",
    data: {
      supplierId,
      amount,
      remark,
    },
  });
}

/**
 * 获取供应商代销状态统计
 */
export function getConsignmentStats() {
  return request({
    url: `/Purchase/SupplierConsignment/getConsignmentStats`,
    method: "get",
  });
}

/**
 * 获取保证金流水记录
 * @param {Object} data 查询参数
 */
export function getDepositHistory(data) {
  return request({
    url: `/Purchase/SupplierDeposit/getDepositHistory`,
    method: "post",
    data,
  });
}

/**
 * 导出保证金流水记录
 * @param {Object} data 查询参数
 */
export function exportDepositHistory(data) {
  return request({
    url: `/Purchase/SupplierDeposit/exportDepositHistory`,
    method: "post",
    responseType: "blob",
    data,
  });
}

/**
 * 获取保证金操作历史
 * @param {Object} data 查询参数
 */
export function getDepositOperationHistory(data) {
  return request({
    url: `/Purchase/SupplierDeposit/getDepositOperationHistory`,
    method: "post",
    data,
  });
}

/**
 * 获取分账规则列表
 * @param {Object} data 查询参数
 */
export function getConsignmentRules(data) {
  return request({
    url: `/Consignment/ConsignmentRule/getRuleList`,
    method: "post",
    data,
  });
}

/**
 * 获取分账规则详情
 * @param {Number} ruleId 规则ID
 */
export function getConsignmentRuleDetail(ruleId) {
  return request({
    url: `/Consignment/ConsignmentRule/getRuleDetail`,
    method: "post",
    data: {
      ruleId,
    },
  });
}

/**
 * 创建分账规则
 * @param {Object} data 规则数据
 */
export function createConsignmentRule(data) {
  // 确保数据格式正确
  const formattedData = formatRuleData(data);

  return request({
    url: `/Consignment/ConsignmentRule/createRule`,
    method: "post",
    data: formattedData,
  });
}

/**
 * 更新分账规则
 * @param {Number} ruleId 规则ID
 * @param {Object} data 规则数据
 */
export function updateConsignmentRule(ruleId, data) {
  // 确保数据格式正确
  const formattedData = formatRuleData(data);

  return request({
    url: `/Consignment/ConsignmentRule/updateRule`,
    method: "post",
    data: {
      ruleId,
      ...formattedData,
    },
  });
}

/**
 * 更新分账规则状态
 * @param {Number} ruleId 规则ID
 * @param {Number} status 状态值：4-禁用，5-启用
 */
export function updateConsignmentRuleStatus(ruleId, status) {
  return request({
    url: `/Consignment/ConsignmentRule/updateRuleStatus`,
    method: "post",
    data: {
      ruleId,
      status,
    },
  });
}

/**
 * 格式化规则数据，确保与后端格式一致
 * @param {Object} data 规则数据
 * @returns {Object} 格式化后的数据
 */
function formatRuleData(data) {
  const formattedData = { ...data };

  // 如果已经有规则内容，直接返回
  if (formattedData.ruleContent) {
    return formattedData;
  }

  // 固定金额模式 (ruleType = 4)
  formattedData.ruleType = 4;

  // 固定为"指定商品"类型 (goodsType = 2)
  formattedData.goodsType = 2;

  // 规则内容为空对象，实际内容在saveRule方法中通过scopeGoods构建
  formattedData.ruleContent = {};

  return formattedData;
}

/**
 * 删除分账规则
 * @param {Number} ruleId 规则ID
 */
export function deleteConsignmentRule(ruleId) {
  return request({
    url: `/Consignment/ConsignmentRule/deleteRule`,
    method: "post",
    data: {
      ruleId,
    },
  });
}

/**
 * 设置规则优先级
 * @param {Array} ruleIds 规则ID数组
 */
export function setRulePriorities(ruleIds) {
  return request({
    url: `/Consignment/ConsignmentRule/setRulePriorities`,
    method: "post",
    data: {
      ruleIds,
    },
  });
}

/**
 * 检测规则冲突
 * @param {Number} ruleId 规则ID
 */
export function checkRuleConflicts(ruleId) {
  return request({
    url: `/Consignment/ConsignmentRule/checkRuleConflicts`,
    method: "post",
    data: {
      ruleId,
    },
  });
}

/**
 * 批量导入规则
 * @param {FormData} formData 包含Excel文件的FormData
 */
export function importConsignmentRules(formData) {
  return request({
    url: `/Consignment/ConsignmentRule/importConsignmentRules`,
    method: "post",
    data: formData,
    headers: {
      "Content-Type": "multipart/form-data",
    },
  });
}

/**
 * 导出规则模板
 */
export function exportRuleTemplate() {
  return request({
    url: `/Consignment/ConsignmentRule/exportRuleTemplate`,
    method: "get",
    responseType: "blob",
  });
}

/**
 * 导出规则列表
 * @param {Object} data 查询参数
 */
export function exportConsignmentRules(data) {
  return request({
    url: `/Consignment/ConsignmentRule/exportConsignmentRules`,
    method: "post",
    responseType: "blob",
    data,
  });
}

/**
 * 获取结算周期配置
 * @param {Number} supplierId 供应商ID，不传则获取默认配置
 */
export function getSettlementCycle(supplierId) {
  return request({
    url: `/Finance/SettlementCycle/getSettlementCycle`,
    method: "post",
    data: {
      supplierId,
    },
  });
}

/**
 * 更新结算周期配置
 * @param {Object} data 配置数据
 */
export function updateSettlementCycle(data) {
  return request({
    url: `/Finance/SettlementCycle/updateSettlementCycle`,
    method: "post",
    data,
  });
}

/**
 * 获取结算单列表
 * @param {Object} data 查询参数
 */
export function getSettlementOrders(data) {
  return request({
    url: `/Finance/SettlementOrder/getSettlementOrders`,
    method: "post",
    data,
  });
}

/**
 * 获取结算单详情
 * @param {Number} orderId 结算单ID
 */
export function getSettlementOrderDetail(orderId) {
  return request({
    url: `/Finance/SettlementOrder/getSettlementOrderDetail`,
    method: "post",
    data: {
      orderId,
    },
  });
}

/**
 * 手动生成结算单（通用接口）
 * @param {Object} data 结算单数据
 * @param {Number} data.supplierId 供应商ID
 * @param {Number} data.settlementStartTime 结算开始时间Unix时间戳（秒为单位）
 * @param {Number} data.settlementEndTime 结算结束时间Unix时间戳（秒为单位）
 * @param {String} data.remark 备注
 */
export function manualGenerateSettlement(data) {
  return request({
    url: `/Finance/SettlementOrder/manualGenerateSettlement`,
    method: "post",
    data,
  });
}

/**
 * 审核结算单
 * @param {Object} data 审核数据
 * @param {Number} data.settlementId 结算单ID
 * @param {String} data.settlementNo 结算单号
 */
export function auditSettlementOrder(data) {
  return request({
    url: `/Finance/SettlementOrder/auditSettlementOrder`,
    method: "post",
    data,
  });
}

/**
 * 批量审核结算单
 * @param {Object} data 批量审核数据
 * @param {Array} data.settlementIds 结算单ID数组
 */
export function batchAuditSettlementOrders(data) {
  return request({
    url: `/Finance/SettlementOrder/batchAuditSettlementOrders`,
    method: "post",
    data,
  });
}

/**
 * 获取代销库存列表
 * @param {Object} data 查询参数
 */
export function getConsignmentInventory(data) {
  return request({
    url: `/Stock/ConsignmentInventory/getConsignmentInventory`,
    method: "post",
    data,
  });
}

/**
 * 获取代销库存变更记录
 * @param {Object} data 查询参数
 */
export function getInventoryChangeRecords(data) {
  return request({
    url: `/Stock/ConsignmentInventory/getInventoryChangeRecords`,
    method: "post",
    data,
  });
}

/**
 * 锁定代销库存
 * @param {Number} supplierId 供应商ID
 * @param {Number} skuId 商品SKU ID
 * @param {Number} quantity 数量
 * @param {String} remark 备注
 */
export function lockInventory(supplierId, skuId, quantity, remark = "") {
  return request({
    url: `/Stock/ConsignmentInventory/lockInventory`,
    method: "post",
    data: {
      supplierId,
      skuId,
      quantity,
      remark,
    },
  });
}

/**
 * 解锁代销库存
 * @param {Number} supplierId 供应商ID
 * @param {Number} skuId 商品SKU ID
 * @param {Number} quantity 数量
 * @param {String} remark 备注
 */
export function unlockInventory(supplierId, skuId, quantity, remark = "") {
  return request({
    url: `/Purchase/SupplierConsignment/unlockInventory`,
    method: "post",
    data: {
      supplierId,
      skuId,
      quantity,
      remark,
    },
  });
}

/**
 * 获取分账明细列表
 * @param {Object} data 查询参数
 */
export function getSettlementDetailList(data) {
  return request({
    url: `/Finance/ConsignmentSettlement/getSettlementDetailList`,
    method: "post",
    data,
  });
}

/**
 * 获取分账统计数据
 * @param {Object} data 查询参数
 */
export function getSettlementStatistics(data) {
  return request({
    url: `/Purchase/SupplierConsignment/getSettlementStatistics`,
    method: "post",
    data,
  });
}

/**
 * 获取扩展趋势分析数据
 * @param {Object} data 查询参数
 * @param {string} data.period 统计周期: day/week/month/quarter/year
 * @param {string} data.dimension 数据维度: amount/count/supplier/efficiency
 * @param {number} data.supplierId 供应商ID（可选）
 * @param {number} data.settlementStatus 结算状态（可选）
 * @param {number} data.startDate 开始时间戳（可选）
 * @param {number} data.endDate 结束时间戳（可选）
 * @param {number} data.limit 数据条数限制（可选，默认30）
 */
export function getExtendedTrendAnalysis(data) {
  return request({
    url: `/Purchase/SupplierConsignment/getExtendedTrendAnalysis`,
    method: "post",
    data,
  });
}

/**
 * 获取供应商结算记录列表
 * @param {Object} data 查询参数
 */
export function getSupplierSettlementRecords(data) {
  return request({
    url: `/Finance/ConsignmentSettlement/getSupplierSettlementRecords`,
    method: "post",
    data,
  });
}

/**
 * 获取供应商结算统计数据
 * @param {Object} data 查询参数
 */
export function getSupplierSettlementStatistics(data) {
  return request({
    url: `/Finance/ConsignmentSettlement/getSupplierSettlementStatistics`,
    method: "post",
    data,
  });
}

/**
 * 获取供应商结算详情
 * @param {Number} id 结算记录ID
 */
export function getSupplierSettlementDetail(id) {
  return request({
    url: `/Finance/ConsignmentSettlement/getSupplierSettlementDetail`,
    method: "post",
    data: {
      id,
    },
  });
}

/**
 * 获取订单结算信息
 * @param {Number} orderId 订单ID
 */
export function getOrderSettlementInfo(orderId) {
  return request({
    url: `/Finance/ConsignmentSettlement/getOrderSettlementInfo`,
    method: "post",
    data: {
      orderId,
    },
  });
}





/**
 * 获取仓库商品列表
 * @param {Object} data 查询参数
 */
export function getWarehouseGoods(data) {
  return request({
    url: `/Purchase/SupplierConsignment/getWarehouseGoods`,
    method: "post",
    data,
  });
}

/**
 * 获取供应商仓库商品列表
 * 只返回特定供应商在特定仓库的库存
 * @param {Object} data 查询参数
 */
export function getSupplierWarehouseGoods(data) {
  return request({
    url: `/Purchase/SupplierConsignment/getSupplierWarehouseGoods`,
    method: "post",
    data,
  });
}

/**
 * 获取结算单列表
 * @param {Object} data 查询参数
 */
export function getSettlementList(data) {
  return request({
    url: `/Purchase/SupplierConsignment/getSettlementList`,
    method: "post",
    data,
  });
}

/**
 * 获取结算单详情
 * @param {Number} settlementId 结算单ID
 */
export function getSettlementDetail(settlementId) {
  return request({
    url: `/Purchase/SupplierConsignment/getSettlementDetail`,
    method: "post",
    data: {
      settlementId,
    },
  });
}

/**
 * 导出结算单列表
 * @param {Object} data 查询参数
 */
export function exportSettlementList(data) {
  return request({
    url: `/Purchase/SupplierConsignment/exportSettlementList`,
    method: "post",
    responseType: "blob",
    data,
  });
}



/**
 * 获取当前登录供应商的保证金账户余额
 * 供应商角色端使用
 */
export function getSupplierOwnDepositAccount() {
  return request({
    url: `/Purchase/SupplierDeposit/getSupplierOwnDepositAccount`,
    method: "post",
  });
}

/**
 * 获取当前登录供应商的保证金流水记录
 * 供应商角色端使用
 * @param {Object} data 查询参数
 */
export function getSupplierOwnDepositHistory(data) {
  return request({
    url: `/Purchase/SupplierDeposit/getSupplierOwnDepositHistory`,
    method: "post",
    data,
  });
}

/**
 * 获取供应商分账明细列表
 * 供应商角色端使用
 * @param {Object} data 查询参数
 */
export function getSupplierSettlementDetailList(data) {
  return request({
    url: `/Finance/SupplierSettlementDetail/getSupplierSettlementDetailList`,
    method: "post",
    data,
  });
}

/**
 * 获取供应商分账明细详情
 * 供应商角色端使用
 * @param {Number} id 分账明细ID
 */
export function getSupplierSettlementDetailInfo(id) {
  return request({
    url: `/Finance/SupplierSettlementDetail/getSupplierSettlementDetailInfo`,
    method: "post",
    data: {
      id,
    },
  });
}